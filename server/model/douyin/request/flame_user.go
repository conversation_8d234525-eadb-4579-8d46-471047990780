package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type FlameUserSearch struct {
	request.PageInfo
	Nickname   string `json:"nickname" form:"nickname"`
	UniqueId   string `json:"uniqueId" form:"uniqueId"`
	CategoryId uint   `json:"categoryId" form:"categoryId"`
}

type AddFlameUserRequest struct {
	ImToken    string `json:"imToken" binding:"required"`
	CategoryId uint   `json:"categoryId" binding:"required"`
	Did        string `json:"did"`
}

type ToggleCollectEnabledRequest struct {
	ID      uint `json:"id" binding:"required"`
	Enabled bool `json:"enabled"`
}

type UpdateFlameUserIPRequest struct {
	ID     uint   `json:"id" binding:"required"`
	BindIP string `json:"bindIP" binding:"required"`
}

type BindFlameDeviceRequest struct {
	ID  uint   `json:"id" binding:"required"`
	Did string `json:"did" binding:"required"`
}
