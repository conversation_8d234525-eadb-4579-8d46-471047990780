package request

// VoiceCloneTaskRequest 创建人声克隆任务请求
type VoiceCloneTaskRequest struct {
	Text               string  `json:"text" form:"text" binding:"required"`                         // 要转换的文本
	Language           string  `json:"language" form:"language"`                                    // 语言，默认"zh"
	Model              string  `json:"model" form:"model"`                                          // 模型选择，默认"Index-TTS"
	Speed              float64 `json:"speed" form:"speed"`                                          // 语速，默认1.0
	Temperature        float64 `json:"temperature" form:"temperature"`                              // 温度，默认1.0
	TopP               float64 `json:"topP" form:"topP"`                                            // TopP，默认0.8
	TopK               int     `json:"topK" form:"topK"`                                            // TopK，默认30
	RepetitionPenalty  float64 `json:"repetitionPenalty" form:"repetitionPenalty"`                  // 重复惩罚，默认10
	LengthPenalty      float64 `json:"lengthPenalty" form:"lengthPenalty"`                          // 长度惩罚，默认0
	NumBeams           int     `json:"numBeams" form:"numBeams"`                                    // 束搜索数量，默认3
	MaxMelTokens       int     `json:"maxMelTokens" form:"maxMelTokens"`                            // 最大mel tokens，默认600
	SentenceSplit      string  `json:"sentenceSplit" form:"sentenceSplit"`                          // 句子分割方式，默认"auto"
	TargetCategoryId   uint    `json:"targetCategoryId" form:"targetCategoryId" binding:"required"` // 目标分类ID
	ReferenceAudioUrl  string  `json:"referenceAudioUrl" binding:"required"`                        // 参考音频文件URL
	ReferenceAudioName string  `json:"referenceAudioName"`                                          // 参考音频文件名称
}

// VoiceCloneTaskListRequest 人声克隆任务列表请求
type VoiceCloneTaskListRequest struct {
	Page     int `json:"page" form:"page"`         // 页码
	PageSize int `json:"pageSize" form:"pageSize"` // 每页数量
}
