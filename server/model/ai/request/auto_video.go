package request

import "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"

// 自动成片任务参数
type AutoVideoTaskParams struct {
	TaskName                 string         `json:"taskName" form:"taskName"`                                 // 任务名称
	VideoVolume              float64        `json:"videoVolume" form:"videoVolume"`                           // 视频原生音量
	VoiceVolume              float64        `json:"voiceVolume" form:"voiceVolume"`                           // 配音音量
	BackgroundVolume         float64        `json:"backgroundVolume" form:"backgroundVolume"`                 // 背景音乐音量
	VoiceSpeed               float64        `json:"voiceSpeed" form:"voiceSpeed"`                             // 语音配速
	Contrast                 int            `json:"contrast" form:"contrast"`                                 // 对比度
	Saturation               int            `json:"saturation" form:"saturation"`                             // 饱和度
	Brightness               int            `json:"brightness" form:"brightness"`                             // 亮度
	Hue                      int            `json:"hue" form:"hue"`                                           // 色度
	RandomTransition         bool           `json:"randomTransition" form:"randomTransition"`                 // 随机转场
	RandomFilter             bool           `json:"randomFilter" form:"randomFilter"`                         // 随机滤镜
	RandEffect               bool           `json:"randEffect" form:"randEffect"`                             // 随机特效
	EffectProbability        float64        `json:"effectProbability" form:"effectProbability"`               // 特效应用概率
	ShowSubtitle             bool           `json:"showSubtitle" form:"showSubtitle"`                         // 显示字幕
	GlobalVoice              bool           `json:"globalVoice" form:"globalVoice"`                           // 全局配音
	GlobalSubtitleList       []string       `json:"globalSubtitleList" form:"globalSubtitleList"`             // 全局字幕列表
	SubtitleStyle            GlobalSubtitle `json:"subtitleStyle" form:"subtitleStyle"`                       // 字幕样式
	ActorVoice               string         `json:"actorVoice" form:"actorVoice"`                             // 配音演员
	LensMaterials            []LensMaterial `json:"lensMaterials" form:"lensMaterials"`                       // 镜头素材
	TitleList                []Title        `json:"titleList" form:"titleList"`                               // 标题列表
	BackgroundMusics         []string       `json:"backgroundMusics" form:"backgroundMusics"`                 // 背景音乐
	BackgroundMusicDurations []float64      `json:"backgroundMusicDurations" form:"backgroundMusicDurations"` // 背景音乐时长
	Stickers                 []Sticker      `json:"stickers" form:"stickers"`                                 // 贴纸列表
	AspectRatio              string         `json:"aspectRatio" form:"aspectRatio"`                           // 视频比例
	OutputMode               string         `json:"outputMode" form:"outputMode"`                             // 输出模式：视频时长/配音时长
	GenerateCount            int            `json:"generateCount" form:"generateCount"`                       // 生成数量
	TransitionList           []string       `json:"transitionList" form:"transitionList"`                     // 转场列表
	FilterList               []string       `json:"filterList" form:"filterList"`                             // 滤镜列表
	EffectList               []string       `json:"effectList" form:"effectList"`                             // 特效列表
	CategoryIds              []uint         `json:"categoryIds" form:"categoryIds"`                           // 视频分类ID列表
	Topics                   [][]string     `json:"topics" form:"topics"`                                     // 话题列表 (二维数组)
	UseMusicDuration         bool           `json:"useMusicDuration" form:"useMusicDuration"`                 // 使用音乐时长
	DisableRandomSubtitle    bool           `json:"disableRandomSubtitle" form:"disableRandomSubtitle"`       // 禁用随机字幕
	ManualProductId          uint           `json:"manualProductId" form:"manualProductId"`                   // 手动录入商品ID
}

// 全局字幕
type GlobalSubtitle struct {
	Content       string    `json:"content" form:"content"`             // 字幕内容
	Height        float64   `json:"height" form:"height"`               // 文字高度
	FontFamily    string    `json:"fontFamily" form:"fontFamily"`       // 字体
	FontSize      int       `json:"fontSize" form:"fontSize"`           // 字号
	Alignment     string    `json:"alignment" form:"alignment"`         // 对齐方式
	FontStyle     FontStyle `json:"fontStyle" form:"fontStyle"`         // 字体样式
	FlowerStyle   string    `json:"flowerStyle" form:"flowerStyle"`     // 花字样式
	RandomEffect  bool      `json:"randomEffect" form:"randomEffect"`   // 使用随机效果
	ImageDuration int       `json:"imageDuration" form:"imageDuration"` // 图片素材的持续时长，单位秒，默认2秒
}

// 字体样式
type FontStyle struct {
	Type            string `json:"type" form:"type"`                       // 样式类型：normal/flower/main
	Color           string `json:"color" form:"color"`                     // 字体颜色
	StyleType       string `json:"styleType" form:"styleType"`             // 样式类型：none/background/border
	BackgroundColor string `json:"backgroundColor" form:"backgroundColor"` // 背景颜色
	BorderColor     string `json:"borderColor" form:"borderColor"`         // 边框颜色
	BorderWidth     int    `json:"borderWidth" form:"borderWidth"`         // 边框宽度
}

// 镜头素材
type LensMaterial struct {
	MediaId          string       `json:"mediaId" form:"mediaId"`                   // 媒体ID
	MediaUrl         string       `json:"mediaUrl" form:"mediaUrl"`                 // 媒体URL
	Type             string       `json:"type" form:"type"`                         // 类型：video/image
	Narration        string       `json:"narration" form:"narration"`               // 口播内容（单个，兼容旧版本）
	Narrations       []string     `json:"narrations" form:"narrations"`             // 口播内容（多个）
	ExtraMedia       []ExtraMedia `json:"extraMedia" form:"extraMedia"`             // 额外素材
	SplitMode        string       `json:"splitMode" form:"splitMode"`               // 剪辑模式：AverageSplit/NoSplit，智能剪辑/完整播放，默认AverageSplit
	Duration         float64      `json:"duration" form:"duration"`                 // 素材时长
	UseMusicDuration bool         `json:"useMusicDuration" form:"useMusicDuration"` // 使用音乐时长
}

// 额外素材
type ExtraMedia struct {
	ResourceId string `json:"resourceId" form:"resourceId"` // 资源ID
	MediaId    string `json:"mediaId" form:"mediaId"`       // 媒体ID
	Name       string `json:"name" form:"name"`             // 名称
	Type       string `json:"type" form:"type"`             // 类型
	Url        string `json:"url" form:"url"`               // URL
}

// 标题
type Title struct {
	Content      string    `json:"content" form:"content"`           // 标题内容（兼容性字段，主要用于主标题）
	Contents     []string  `json:"contents" form:"contents"`         // 副标题内容数组（每组可包含多个文案）
	Height       float64   `json:"height" form:"height"`             // 文字高度
	FontFamily   string    `json:"fontFamily" form:"fontFamily"`     // 字体
	FontSize     int       `json:"fontSize" form:"fontSize"`         // 字号
	Alignment    string    `json:"alignment" form:"alignment"`       // 对齐方式
	FontStyle    FontStyle `json:"fontStyle" form:"fontStyle"`       // 字体样式
	FlowerStyle  string    `json:"flowerStyle" form:"flowerStyle"`   // 花字样式
	RandomEffect bool      `json:"randomEffect" form:"randomEffect"` // 使用随机效果
	Type         string    `json:"type" form:"type"`                 // 标题类型：main/sub
}

// 贴纸
type Sticker struct {
	MediaId  string  `json:"mediaId" form:"mediaId"`   // 媒体ID
	MediaUrl string  `json:"mediaUrl" form:"mediaUrl"` // 媒体URL
	X        float64 `json:"x" form:"x"`               // X坐标
	Y        float64 `json:"y" form:"y"`               // Y坐标
	Width    float64 `json:"width" form:"width"`       // 宽度
	Height   float64 `json:"height" form:"height"`     // 高度
}

// 自动成片任务列表查询参数
type AutoVideoTaskListParams struct {
	request.PageInfo        // 分页信息
	Status           *int   `json:"status" form:"status"`   // 任务状态
	Keyword          string `json:"keyword" form:"keyword"` // 关键词
}

// 素材库请求参数
type ResourceLibraryParams struct {
	request.PageInfo        // 分页信息
	Type             string `json:"type" form:"type"`         // 素材类型：video/image/audio
	Category         string `json:"category" form:"category"` // 分类ID
	Keyword          string `json:"keyword" form:"keyword"`   // 关键词
}

// AIScriptRequest AI文案生成请求
type AIScriptRequest struct {
	Prompt         string `json:"prompt"`         // 提示词
	PromptTemplate string `json:"promptTemplate"` // 提示词模板（兼容旧版本）
	InputText      string `json:"inputText"`      // 输入的提示文本
	GenerateCount  int    `json:"generateCount"`  // 生成数量
	MinDuration    int    `json:"minDuration"`    // 口播最短时长(秒)
	MaxDuration    int    `json:"maxDuration"`    // 口播最长时长(秒)
	Model          string `json:"model"`          // 使用的模型，默认为deepseek-v3-250324
}

// 贴纸模板保存参数
type StickerTemplateParams struct {
	Name        string  `json:"name" form:"name"`               // 模板名称
	Description string  `json:"description" form:"description"` // 模板描述
	ImageId     uint    `json:"imageId" form:"imageId"`         // 贴纸图片ID
	Width       float64 `json:"width" form:"width"`             // 贴纸宽度
	Height      float64 `json:"height" form:"height"`           // 贴纸高度
	X           float64 `json:"x" form:"x"`                     // X坐标
	Y           float64 `json:"y" form:"y"`                     // Y坐标
}
