package response

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/ai"
)

// VideoTaskResponse 视频任务响应
type VideoTaskResponse struct {
	TaskId string `json:"taskId"`
}

// VideoTaskStatusResponse 视频任务状态响应
type VideoTaskStatusResponse struct {
	TaskId   string     `json:"taskId"`
	Status   string     `json:"status"`
	VideoUrl string     `json:"videoUrl"`
	ErrorMsg string     `json:"errorMsg"`
	EndTime  *time.Time `json:"endTime,omitempty"`
}

// VideoTaskListResponse 视频任务列表响应
type VideoTaskListResponse struct {
	List     []ai.VideoTask `json:"list"`
	Total    int64          `json:"total"`
	Page     int            `json:"page"`
	PageSize int            `json:"pageSize"`
}
