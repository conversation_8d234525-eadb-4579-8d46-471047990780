package ai

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// VoiceCloneTask 人声克隆任务
type VoiceCloneTask struct {
	global.GVA_MODEL
	TaskId             string     `json:"taskId" gorm:"column:task_id;comment:任务ID"`                                // 任务ID
	UserId             uint       `json:"userId" gorm:"column:user_id;comment:用户ID"`                                // 用户ID
	Text               string     `json:"text" gorm:"column:text;type:text;comment:要转换的文本"`                         // 要转换的文本
	ReferenceAudioName string     `json:"referenceAudioName" gorm:"column:reference_audio_name;comment:参考音频名称(声源)"` // 参考音频名称(声源)
	Language           string     `json:"language" gorm:"column:language;comment:语言"`                               // 语言
	Model              string     `json:"model" gorm:"column:model;comment:模型"`                                     // 模型
	Speed              float64    `json:"speed" gorm:"column:speed;comment:语速"`                                     // 语速
	Temperature        float64    `json:"temperature" gorm:"column:temperature;comment:温度"`                         // 温度
	TopP               float64    `json:"topP" gorm:"column:top_p;comment:TopP"`                                    // TopP
	TopK               int        `json:"topK" gorm:"column:top_k;comment:TopK"`                                    // TopK
	RepetitionPenalty  float64    `json:"repetitionPenalty" gorm:"column:repetition_penalty;comment:重复惩罚"`          // 重复惩罚
	LengthPenalty      float64    `json:"lengthPenalty" gorm:"column:length_penalty;comment:长度惩罚"`                  // 长度惩罚
	NumBeams           int        `json:"numBeams" gorm:"column:num_beams;comment:束搜索数量"`                           // 束搜索数量
	MaxMelTokens       int        `json:"maxMelTokens" gorm:"column:max_mel_tokens;comment:最大mel tokens"`           // 最大mel tokens
	SentenceSplit      string     `json:"sentenceSplit" gorm:"column:sentence_split;comment:句子分割方式"`                // 句子分割方式
	TargetCategoryId   uint       `json:"targetCategoryId" gorm:"column:target_category_id;comment:目标分类ID"`         // 目标分类ID
	Status             string     `json:"status" gorm:"column:status;comment:任务状态"`                                 // 任务状态
	AudioUrl           string     `json:"audioUrl" gorm:"column:audio_url;comment:生成的音频URL"`                        // 生成的音频URL
	ErrorMsg           string     `json:"errorMsg" gorm:"column:error_msg;comment:错误信息"`                            // 错误信息
	StartTime          time.Time  `json:"startTime" gorm:"column:start_time;comment:开始时间"`                          // 开始时间
	EndTime            *time.Time `json:"endTime" gorm:"column:end_time;comment:结束时间"`                              // 结束时间
	// 音乐库相关信息（audio_url会在保存到音乐库后更新为音乐库URL）
	Duration int64  `json:"duration" gorm:"column:duration;comment:音频时长(秒)"` // 音频时长(秒)
	MediaId  string `json:"mediaId" gorm:"column:media_id;comment:媒资ID"`     // 媒资ID
}

// TableName 指定表名
func (v *VoiceCloneTask) TableName() string {
	return "ai_voice_clone_tasks"
}
