package creative

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// AutoPublishVideoTemplate 自动发布视频模板
type AutoPublishVideoTemplate struct {
	global.GVA_MODEL
	Weekday      int    `gorm:"type:tinyint;default:0;comment:星期天数;index" json:"weekday"`
	Hour         int    `gorm:"type:tinyint;default:0;comment:小时数" json:"hour"`
	Minute       int    `gorm:"type:tinyint;default:0;comment:分钟数" json:"minute"`
	SysUserId    int64  `gorm:"type:bigint;default:0;comment:管理员id;index" json:"sysUserId"`
	Status       int    `gorm:"type:tinyint;default:1;comment:状态：1-启用；2-暂停；3-终止" json:"status"`
	TemplateName string `gorm:"type:varchar(255);default:'';comment:模板名称" json:"templateName"`
	TemplateId   string `gorm:"type:varchar(64);default:'';index;comment:模板id" json:"templateId"`
}

func (AutoPublishVideoTemplate) TableName() string {
	return "auto_publish_video_template"
}
