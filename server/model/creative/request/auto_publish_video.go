package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative"
)

// 获取列表请求参数（实际GET请求使用query参数，此结构体为预留扩展）
type GetAutoPublishVideoListRequest struct {
	DyUserID string `json:"dyUserId" form:"dyUserId"` // 抖音用户ID
}

// 保存操作请求参数
type SaveAutoPublishVideoRequest struct {
	DyUserId uint                        `json:"dyUserId" binding:"required"` // 抖音用户ID
	List     []creative.AutoPublishVideo `json:"list" binding:"required"`     // 发布计划列表
}

type AutoPublishVideoSearch struct {
	request.PageInfo
}

type ChangeStatusRequest struct {
	DyUserId uint `json:"dyUserId" binding:"required"` // 抖音用户id
	Opt      int  `json:"opt" binding:"required"`      // 操作：1-启用 2-禁用 3-删除
}
