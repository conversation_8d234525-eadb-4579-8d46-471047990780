package response

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative"
)

type AiModelResponse struct {
	creative.AiModel
	VideoCategoryName string `json:"videoCategoryName"` // 视频库分类名称
}

type AiModelListResponse struct {
	List     []AiModelResponse `json:"list"`
	Total    int64             `json:"total"`
	Page     int               `json:"page"`
	PageSize int               `json:"pageSize"`
}
