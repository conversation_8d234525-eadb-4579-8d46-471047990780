package douyin

import (
	"fmt"
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	commonReq "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type DyIPApi struct{}

// SyncIPs 同步IP列表
// @Tags DyIP
// @Summary 同步IP列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"同步成功"}"
// @Router /douyin/ip/sync-ips [post]
func (api *DyIPApi) SyncIPs(c *gin.Context) {
	if err := dyIPService.SyncIPs(); err != nil {
		global.GVA_LOG.Error("同步失败!", zap.Error(err))
		response.FailWithMessage("同步失败", c)
		return
	}
	response.OkWithMessage("同步成功", c)
}

// GetIPList 获取IP列表
// @Tags DyIP
// @Summary 获取IP列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "页码, 每页大小"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /douyin/ip/get-ip-list [get]
func (api *DyIPApi) GetIPList(c *gin.Context) {
	var pageInfo commonReq.PageInfo
	_ = c.ShouldBindQuery(&pageInfo)
	userId := utils.GetUserID(c)
	list, total, err := dyIPService.GetIPList(pageInfo, userId)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// UpdateIPStatus 更新IP状态
// @Tags DyIP
// @Summary 更新IP状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.UpdateIPStatus true "ID, 状态"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /douyin/ip/update-status [put]
func (api *DyIPApi) UpdateIPStatus(c *gin.Context) {
	var req request.UpdateIPStatus
	_ = c.ShouldBindJSON(&req)
	if err := dyIPService.UpdateIPStatus(req.ID, req.Status); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// GetIPBindings 获取IP绑定的用户信息
// @Tags DyIP
// @Summary 获取IP绑定的用户信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path uint true "IP ID"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /douyin/ip/bindings/{id} [get]
func (api *DyIPApi) GetIPBindings(c *gin.Context) {
	id, _ := strconv.ParseUint(c.Param("id"), 10, 0)
	if dyUsers, flameUsers, err := dyIPService.GetIPBindings(uint(id)); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	} else {
		response.OkWithData(gin.H{
			"dyUsers":    dyUsers,
			"flameUsers": flameUsers,
		}, c)
	}
}

// GetAvailableIP 获取可用的IP列表
// @Tags DyIP
// @Summary 获取可用的IP列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /douyin/ip/available [get]
func (api *DyIPApi) GetAvailableIP(c *gin.Context) {
	ips, err := dyIPService.GetAvailableIP()
	if err != nil {
		global.GVA_LOG.Error("获取可用IP失败!", zap.Error(err))
		response.FailWithMessage("获取可用IP失败", c)
		return
	}
	response.OkWithData(ips, c)
}

// GetUnseIP 获取未被使用的ip
// @Tags DyIP
// @Summary 获取未被使用的ip
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /douyin/ip/unused [get]
func (api *DyIPApi) GetUnusedIP(c *gin.Context) {
	ip, err := dyIPService.GetUnusedIP()
	if err != nil {
		// 如果记录不存在，返回空字符串
		if err.Error() == "record not found" {
			response.FailWithMessage("没有可用的独立ip", c)
			return
		}

		response.FailWithMessage(fmt.Sprintf("获取失败:%+v", err), c)
		return
	}
	response.OkWithData(ip, c)
}

// BindMacAddresses 绑定MAC地址
// @Tags DyIP
// @Summary 绑定MAC地址到IP
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.BindMacAddresses true "IP ID和MAC地址列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"绑定成功"}"
// @Router /douyin/ip/bind-mac [post]
func (api *DyIPApi) BindMacAddresses(c *gin.Context) {
	var req request.BindMacAddresses
	_ = c.ShouldBindJSON(&req)
	if err := dyIPService.BindMacAddresses(req.ID, req.MacAddresses); err != nil {
		global.GVA_LOG.Error("绑定MAC地址失败!", zap.Error(err))
		response.FailWithMessage("绑定失败", c)
		return
	}
	response.OkWithMessage("绑定成功", c)
}

// UpdateIPRemark 更新IP备注
// @Tags DyIP
// @Summary 更新IP备注
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.UpdateIPRemark true "IP ID和备注内容"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /douyin/ip/update-remark [post]
func (api *DyIPApi) UpdateIPRemark(c *gin.Context) {
	var req request.UpdateIPRemark
	_ = c.ShouldBindJSON(&req)
	if err := dyIPService.UpdateIPRemark(req.ID, req.Remark); err != nil {
		global.GVA_LOG.Error("更新IP备注失败!", zap.Error(err))
		response.FailWithMessage("更新备注失败", c)
		return
	}
	response.OkWithMessage("更新备注成功", c)
}

// GetCopyList 获取复制列表数据
// @Tags DyIP
// @Summary 获取复制列表数据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /douyin/ip/copy-list [get]
func (api *DyIPApi) GetCopyList(c *gin.Context) {
	userId := utils.GetUserID(c)
	copyData, err := dyIPService.GetCopyList(userId)
	if err != nil {
		global.GVA_LOG.Error("获取复制数据失败!", zap.Error(err))
		response.FailWithMessage("获取复制数据失败", c)
		return
	}
	response.OkWithData(copyData, c)
}

func (api *DyIPApi) KIP(c *gin.Context) {
	copyData, err := dyIPService.GetCopyList(1)
	if err != nil {
		global.GVA_LOG.Error("获取复制数据失败!", zap.Error(err))
		response.FailWithMessage("获取复制数据失败", c)
		return
	}
	c.String(200, copyData)
}
