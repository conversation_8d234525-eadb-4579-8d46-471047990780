package douyin

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/logic"
	logicRequest "github.com/flipped-aurora/gin-vue-admin/server/logic/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

/**
* 根据MoreAPI编写用户流程
 */
type DyUserForMoreApi struct{}

func (d *DyUserForMoreApi) GetUserInfo(c *gin.Context) {
	var req request.DyUserMoreGetUserInfoRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	var dyUser *douyin.DyUser
	if req.UniqueId != "" {
		dyUser, _ = dyUserService.GetUserByUniqueId(req.UniqueId)
	}
	if dyUser == nil || dyUser.ID == 0 {
		response.OkWithData(nil, c)
	} else {
		response.OkWithData(dyUser, c)
	}
}

/**
* 获取登录二维码
 */
func (d *DyUserForMoreApi) GetQRCode(c *gin.Context) {
	var req request.DyUserMoreGetQRCodeRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	var proxy string
	var formData request.MoreCreatorApiGetQRCodeRequest
	var dyUser *douyin.DyUser
	if req.UniqueId != "" {
		dyUser, _ = dyUserService.GetUserByUniqueId(req.UniqueId)
		res, err := moreApiService.GetUserInfoByUniqueId(req.UniqueId)
		if err != nil {
			response.FailWithMessage(fmt.Sprintf("获取用户信息失败: %v", err), c)
			return
		}
		if res.Data.StatusMsg != "" {
			response.FailWithMessage("请输入正确的抖音号", c)
			return
		}
	}

	if dyUser.ID != 0 {
		proxy = dyUser.BindIP
		formData = request.MoreCreatorApiGetQRCodeRequest{
			Did:   dyUser.Iid,
			Iid:   dyUser.Did,
			Proxy: proxy,
		}
	} else {
		// 尝试从IP池中获取可用IP
		// availableIPs, err := dyIPService.GetAvailableIP()
		// if err == nil && len(availableIPs) > 0 {
		// 	// 如果有可用IP，选择第一个（已按用户数排序）
		// 	proxy = availableIPs[0]
		// }
		if req.Proxy == "" {
			response.FailWithMessage("没有获取代理，授权失败", c)
			return
		}
		proxy = req.Proxy
		formData = request.MoreCreatorApiGetQRCodeRequest{
			Proxy: proxy,
		}
	}

	// 企业号每次登录都获取新的iid did
	if req.AccountType == 2 || dyUser.AccountType == 2 {
		deviceInfoResp, err := moreCreatorApiService.DeviceRegister()
		if err != nil {
			response.FailWithMessage("企业号注册设备信息失败: "+err.Error(), c)
			return
		}
		if deviceInfoResp.Data.DeviceIdStr == "" || deviceInfoResp.Data.InstallIdStr == "" {
			response.FailWithMessage("获取企业号设备信息失败: Iid和Did为空", c)
			return
		}

		formData.Did = deviceInfoResp.Data.DeviceIdStr
		formData.Iid = deviceInfoResp.Data.InstallIdStr
	}

	resp, err := moreCreatorApiService.GetQRCode(formData)
	if err != nil {
		response.FailWithMessage("获取登录二维码失败: "+err.Error(), c)
		return
	}

	obj := request.RedisToken{
		Did:         resp.Data.Did,
		Iid:         resp.Data.Iid,
		Proxy:       proxy,
		UniqueId:    req.UniqueId,
		CategoryId:  req.CategoryId,
		AccountType: req.AccountType,
	}
	objBytes, _ := json.Marshal(obj)
	err = global.GVA_REDIS.Set(
		context.Background(),
		fmt.Sprintf("more-api-token:%s", resp.Data.Data.Token),
		objBytes,
		time.Minute*30,
	).Err()
	if err != nil {
		response.FailWithMessage("保存token失败: "+err.Error(), c)
		return
	}

	res := map[string]any{
		"token":       resp.Data.Data.Token,
		"qr_code_url": resp.Data.Data.QRCodeIndexUrl,
		"did":         resp.Data.Did,
		"iid":         resp.Data.Iid,
		"proxy":       proxy,
	}
	response.OkWithData(res, c)
}

/**
* 检查扫码状态
 */
func (d *DyUserForMoreApi) CheckQrCode(c *gin.Context) {
	var req request.DyUserMoreCheckQRCodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取token
	tokenStr, err := global.GVA_REDIS.Get(
		context.Background(),
		fmt.Sprintf("more-api-token:%s", req.Token),
	).Result()
	if err != nil {
		response.FailWithMessage("token无效或已过期", c)
		return
	}
	var tokenObj request.RedisToken
	if err = json.Unmarshal([]byte(tokenStr), &tokenObj); err != nil {
		response.FailWithMessage(fmt.Sprintf("解析token失败：%v", err), c)
		return
	}
	// token校验
	formData := request.MoreCreatorApiCheckQRCodeRequest{
		Token: req.Token,
		Proxy: tokenObj.Proxy,
	}

	resp, err := moreCreatorApiService.CheckQRCode(formData)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp.Data.Data.Status != "" {
		response.OkWithDetailed(map[string]interface{}{
			"code":   resp.Data.Data.ErrorCode,
			"status": resp.Data.Data.Status,
		}, "状态", c)
		return
	}

	if resp.Data.Data.ErrorCode != 0 {
		jsonByte, _ := json.Marshal(map[string]any{
			"encryptUid":          resp.Data.Data.EncryptUid,
			"encryptOperStaffUid": resp.Data.Data.CommonParams.EncryptOperStaffUid,
		})
		if resp.Data.Data.EncryptUid != "" {
			err = global.GVA_REDIS.Set(
				context.Background(),
				fmt.Sprintf("more-api-token:encrypt-uid:%s", req.Token),
				string(jsonByte),
				time.Minute*30,
			).Err()
			if err != nil {
				response.FailWithMessage("保存token失败: "+err.Error(), c)
				return
			}
		}
		if resp.Data.Data.ErrorCode == 2046 {
			response.OkWithDetailed(map[string]interface{}{
				"code":    resp.Data.Data.ErrorCode,
				"message": resp.Data.Data.Description,
			}, "需要发送短信并校验", c)
			return
		}
		response.FailWithMessage(fmt.Sprintf("检查二维码状态失败，code:%d, description:%s", resp.Data.Data.ErrorCode, resp.Data.Data.Description), c)
		return
	}

	// 当resp中cookie存在时
	if resp.Data.Cookie != "" {
		// 保存cookie
		_ = dyUserService.SetAwemeCookie(tokenObj.UniqueId, resp.Data.Cookie)

		var dyUser douyin.DyUser
		isNew := false
		// 2.如果没有报错，则将用户信息保存到数据库中dy_user表中
		OldUser, _ := dyUserService.GetUserByUniqueId(tokenObj.UniqueId)
		// 新用户先初始化信息
		if OldUser.ID == 0 {
			isNew = true
			// 开启事务
			tx := global.GVA_DB.Begin()
			defer func() {
				if r := recover(); r != nil {
					tx.Rollback()
					response.FailWithMessage(fmt.Sprintf("执行事务失败：%+v", r), c)
					return
				}
			}()

			userId := utils.GetUserID(c)
			dyUser.SysUserId = int64(userId)
			dyUser.Status = 1
			dyUser.Did = tokenObj.Did
			dyUser.Iid = tokenObj.Iid
			dyUser.CategoryId = tokenObj.CategoryId
			dyUser.BindIP = tokenObj.Proxy
			dyUser.AccountType = tokenObj.AccountType
			dyUser.UniqueId = tokenObj.UniqueId

			// 将软删除的用户彻底删除
			var existingUser douyin.DyUser
			err = tx.Unscoped().Where("unique_id = ?", dyUser.UniqueId).First(&existingUser).Error
			if existingUser.DeletedAt.Valid {
				err = tx.Unscoped().Delete(&douyin.DyUser{}, "id = ?", existingUser.ID).Error // 硬删除已软删除的记录
				if err != nil {
					global.GVA_LOG.Error("清空用户信息失败", zap.Error(err))
					tx.Rollback()
					return
				}
			}

			err = tx.Model(&douyin.DyUser{}).Create(&dyUser).Error
			if err != nil {
				tx.Rollback()
				global.GVA_LOG.Error("保存用户信息失败", zap.Error(err))
				return
			}

			// 更新ip_pool表中的用户数
			err = tx.Model(&douyin.IpPool{}).
				Where("ip = ?", tokenObj.Proxy).
				Update("user_count", gorm.Expr("user_count + ?", 1)).Error
			if err != nil {
				tx.Rollback()
				global.GVA_LOG.Error("更新IP池用户数失败", zap.Error(err))
				return
			}

			// 提交事务
			if err := tx.Commit().Error; err != nil {
				tx.Rollback()
				global.GVA_LOG.Error("事务提交失败", zap.Error(err))
				return
			}
		}

		// 调用GetLoginUserInfo方法，获取用户数据
		reqData := request.MoreCreatorApiVGetLoginUserInfoRequest{
			Cookie: resp.Data.Cookie,
		}
		userInfo, err := moreCreatorApiService.GetLoginUserInfo(reqData)
		if err != nil {

			if userInfo.CookieCode == 1 {
				dyUser.Status = 2
			} else {
				dyUser.Status = 3
			}

		} else {
			dyUser.Status = 1
			dyUser.Did = tokenObj.Did
			dyUser.Iid = tokenObj.Iid
			dyUser.CategoryId = tokenObj.CategoryId
			dyUser.UID = userInfo.Data.User.Uid

			avatarBytes, _ := json.Marshal(userInfo.Data.User.AvatarLarger)
			avatar := string(avatarBytes)
			dyUser.Avatar = avatar
			dyUser.Nickname = userInfo.Data.User.Nickname
			dyUser.UniqueId = userInfo.Data.User.UniqueId
			dyUser.ShortId = userInfo.Data.User.ShortId
			dyUser.SecUid = userInfo.Data.User.SecUid
			dyUser.FollowingCount = userInfo.Data.User.FollowerCount
			dyUser.FollowerCount = userInfo.Data.User.FollowerCount
			dyUser.AwemeCount = userInfo.Data.User.AwemeCount
			totalFavorited, err := strconv.ParseInt(userInfo.Data.User.TotalFavorited, 10, 64)
			if err != nil {
				response.FailWithMessage("转换 TotalFavorited 失败: "+err.Error(), c)
				return
			}
			dyUser.TotalFavorited = totalFavorited
			dyUser.AccountRegion = userInfo.Data.User.AccountRegion
			dyUser.Signature = userInfo.Data.User.Signature
			dyUser.BindIP = tokenObj.Proxy
			dyUser.AccountType = tokenObj.AccountType
		}

		// 更新用户信息
		_ = global.GVA_DB.Model(&dyUser).Updates(&dyUser).Error

		if dyUser.Status == 2 {
			response.FailWithMessage("授权失败："+err.Error(), c)
		} else if dyUser.Status == 3 {
			if isNew {
				response.FailWithMessage("账号注册完成，等待系统自动确认登录状态", c)
				return
			} else {
				response.FailWithMessage("刷新完成，系统将自动确认登录状态", c)
				return
			}
		}
	}

	response.Ok(c)
	return
}

/**
* 发送验证码
 */
func (d *DyUserForMoreApi) SendCaptcha(c *gin.Context) {
	var req request.DyUserMoreSendCaptchaRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 从 Redis 中获取 token 对应的信息
	tokenStr, err := global.GVA_REDIS.Get(
		context.Background(),
		fmt.Sprintf("more-api-token:%s", req.Token),
	).Result()
	if err != nil {
		response.FailWithMessage("token无效或已过期", c)
		return
	}
	var tokenObj request.RedisToken
	if err = json.Unmarshal([]byte(tokenStr), &tokenObj); err != nil {
		response.FailWithMessage(fmt.Sprintf("解析token失败：%v", err), c)
		return
	}

	encryptStr, err := global.GVA_REDIS.Get(
		context.Background(),
		fmt.Sprintf("more-api-token:encrypt-uid:%s", req.Token),
	).Result()
	if err != nil {
		response.FailWithMessage("未找到有效的 encrypt_uid", c)
		return
	}
	var encryptObj struct {
		EncryptUid          string `json:"encryptUid"`
		EncryptOperStaffUid string `json:"encryptOperStaffUid"`
	}
	err = json.Unmarshal([]byte(encryptStr), &encryptObj)
	if err != nil {
		response.FailWithMessage("解析encryptObj失败", c)
		return
	}

	sendformData := request.MoreCreatorApiSendCaptchaRequest{
		Token:               req.Token,
		Proxy:               tokenObj.Proxy,
		EncryptUid:          encryptObj.EncryptUid,
		EncryptOperStaffUid: encryptObj.EncryptOperStaffUid,
	}

	sendResp, err := moreCreatorApiService.SendCaptcha(sendformData)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if sendResp.Data.Data.Mobile != "" {
		response.OkWithMessage(fmt.Sprintf("短信已发送到手机号(%s)中,请注意查收！", sendResp.Data.Data.Mobile), c)
		return
	} else {
		response.FailWithMessage(fmt.Sprintf("发送短信失败: %s", sendResp.Data.Data.Description), c)
		return
	}
}

func (d *DyUserForMoreApi) ValidCaptcha(c *gin.Context) {
	var req request.DyUserMoreValidCaptchaRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 从 Redis 中获取 token 对应的信息
	tokenStr, err := global.GVA_REDIS.Get(
		context.Background(),
		fmt.Sprintf("more-api-token:%s", req.Token),
	).Result()
	if err != nil {
		response.FailWithMessage("token无效或已过期", c)
		return
	}
	var tokenObj request.RedisToken
	if err = json.Unmarshal([]byte(tokenStr), &tokenObj); err != nil {
		response.FailWithMessage(fmt.Sprintf("解析token失败：%v", err), c)
		return
	}

	// 从 Redis 中获取 encrypt_uid
	encryptStr, err := global.GVA_REDIS.Get(
		context.Background(),
		fmt.Sprintf("more-api-token:encrypt-uid:%s", req.Token),
	).Result()
	if err != nil {
		response.FailWithMessage("未找到有效的 encrypt_uid", c)
		return
	}
	var encryptObj struct {
		EncryptUid          string `json:"encryptUid"`
		EncryptOperStaffUid string `json:"encryptOperStaffUid"`
	}
	err = json.Unmarshal([]byte(encryptStr), &encryptObj)
	if err != nil {
		response.FailWithMessage("解析encryptObj失败", c)
		return
	}

	validFormData := request.MoreCreatorApiValidCaptchaRequest{
		Token:      req.Token,
		Code:       req.Code,
		Proxy:      tokenObj.Proxy,
		EncryptUid: encryptObj.EncryptUid,
	}
	if encryptObj.EncryptOperStaffUid != "" {
		validFormData.EncryptOperStaffUid = encryptObj.EncryptOperStaffUid
	}

	validResp, err := moreCreatorApiService.ValidCaptcha(validFormData)
	if err != nil {
		response.FailWithMessage("验证码验证失败: "+err.Error(), c)
		return
	}
	if validResp.Data.Data.Description != "" {
		response.FailWithMessage(fmt.Sprintf("验证码验证失败, description:%s", validResp.Data.Data.Description), c)
		return
	}

	response.Ok(c)
}

func (d *DyUserForMoreApi) GetLoginInfo(c *gin.Context) {
	var req request.DyUserMoreGetLoginInfoRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.OkWithDetailed(nil, err.Error(), c)
		return
	}
	user, err := dyUserService.GetUserByUniqueId(req.UniqueId)
	if err != nil {
		response.OkWithDetailed(user, err.Error(), c)
		return
	}

	dyUserLogic := logic.DyUserLogic{}
	// 登录状态检查
	var loginErr error
	// 强制检查或者状态为1时才会检查
	if req.LoginForce == 1 || user.Status == 1 {
		loginErr = dyUserLogic.CheckLoginInfo(user)
	}

	// 私信状态检查
	var chatErr error
	// 强制检查并且状态为已授权时
	if req.ChatForce == 1 && user.TalkAuthStatus != 0 {
		chatLogic := logic.DyChatLogic{}
		chatReq := logicRequest.DyChatSendMessageRequest{
			SenderId:       int(user.ID),
			DyUserUniqueId: user.UniqueId,
			Message:        "美好的一天即将过去，对自己说加油",
		}
		chatErr = chatLogic.SendMessage(chatReq)
	}
	user, _ = dyUserService.GetUserByUniqueId(req.UniqueId)

	if loginErr != nil {
		response.OkWithDetailed(user, loginErr.Error(), c)
		return
	}
	if chatErr != nil {
		response.OkWithDetailed(user, chatErr.Error(), c)
		return
	}

	response.OkWithData(user, c)
}

// 检查所有账号状态
func (d *DyUserForMoreApi) CheckAllStatus(c *gin.Context) {
	var req request.CheckAllStatusRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	currentUserID := utils.GetUserID(c)

	// 获取当前用户及其下级用户的ID列表
	userIds, err := userService.GetSubordinateUserIds(currentUserID)
	if err != nil {
		response.FailWithMessage("获取用户权限失败: "+err.Error(), c)
		return
	}
	// 添加当前用户ID
	userIds = append(userIds, currentUserID)

	users, err := dyUserService.GetAllUsers(req.CategoryId, userIds)
	if err != nil {
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	dyUserLogic := logic.DyUserLogic{}
	// 遍历用户列表，检查每个用户的登录状态
	checkErrs := []string{}
	var wg sync.WaitGroup
	var mutex sync.Mutex

	for _, user := range users {
		if user.Status != 1 {
			continue
		}

		wg.Add(1)
		chatLogic := logic.DyChatLogic{}
		go func(u douyin.DyUser) {
			defer wg.Done()

			userErr := ""
			loginMaxRetries := 2
			for i := 0; i <= loginMaxRetries; i++ {
				loginErr := dyUserLogic.CheckLoginInfo(&u)
				if loginErr == nil {
					break
				}
				if i == loginMaxRetries {
					if loginErr != nil {
						userErr = fmt.Sprintf("用户【%s】: %s", u.Nickname, loginErr.Error())
					}
				}
			}

			if u.TalkAuthStatus != 0 {
				rand.Seed(time.Now().UnixNano())
				randomNum := rand.Intn(10) + 1
				randomNumStr := strconv.Itoa(randomNum)
				chatReq := logicRequest.DyChatSendMessageRequest{
					SenderId:       int(u.ID),
					DyUserUniqueId: u.UniqueId,
					Message:        randomNumStr,
				}
				chatMaxRetries := 2
				for i := 0; i <= chatMaxRetries; i++ {
					chatErr := chatLogic.SendMessage(chatReq)
					if chatErr == nil {
						break
					}
					if i == chatMaxRetries {
						if chatErr != nil {
							if userErr == "" {
								userErr = fmt.Sprintf("用户【%s】: %s", u.Nickname, chatErr.Error())
							} else {
								userErr = fmt.Sprintf("%s ：%s", userErr, chatErr.Error())
							}

						}
					}
				}
			}

			if userErr != "" {
				mutex.Lock()
				checkErrs = append(checkErrs, userErr)
				mutex.Unlock()
			}
		}(user)
	}

	wg.Wait()
	if len(checkErrs) > 0 {
		response.FailWithMessage(strings.Join(checkErrs, "\n"), c)
		return
	}

	response.Ok(c)
	return
}

// GetUserLatestPost
// @Tags      DyUserForMore
// @Summary   获取用户最新作品
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     uniqueId  query     string                                            true  "抖音号"
// @Success   200      {object}   response.Response{data=response.MoreApiGetUserLatestPostResponse,msg=string}  "获取成功"
// @Router    /douyin-for-more/user/latest-post [get]
func (m *DyUserForMoreApi) GetUserLatestPost(c *gin.Context) {
	var req request.GetUserLatestPostReq
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	posts, err := moreApiService.GetUserLatestPost(req.UniqueId)
	if err != nil {
		global.GVA_LOG.Error("获取用户最新作品失败!", zap.Error(err))
		response.FailWithMessage("获取用户最新作品失败: "+err.Error(), c)
		return
	}
	response.OkWithData(posts, c)
}

// SearchMusic 搜索抖音音乐
// @Tags      DyUserForMore
// @Summary   搜索抖音音乐
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.MoreCreatorApiSearchMusicRequest  true  "搜索参数"
// @Success   200   {object}  response.Response{data=response.MoreCreatorApiSearchMusicResponse,msg=string}  "搜索成功"
// @Router    /douyin-for-more/search-music [get]
func (e *DyUserForMoreApi) SearchMusic(c *gin.Context) {
	var req request.MoreCreatorApiSearchMusicRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 调用服务搜索音乐
	cookie, err := dyUserService.GetAwemeDefaultCookie()
	if err != nil {
		response.FailWithMessage("获取默认cookie失败: "+err.Error(), c)
		return
	}
	if cookie == "" {
		response.FailWithMessage("默认cookie为空", c)
		return
	}
	req.Cookie = cookie
	user, _ := dyUserService.GetUserByUniqueId(douyin.MoreApiRequestUniqueId)
	req.Proxy = user.BindIP
	resp, err := moreApiService.SearchMusic(req)
	if err != nil {
		response.FailWithMessage("搜索音乐失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(resp, "搜索成功", c)
}

// 上传视频
// @Tags      DyUserForMore
// @Summary   上传视频
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.PublishVideoRequest  true  "用户抖音号和视频ID"
// @Success   200   {object}  response.Response{msg=string}  "成功"
// @Router    /douyin-for-more/upload-video [post]
func (e *DyUserForMoreApi) UploadVideo(c *gin.Context) {
	var req request.UploadVideoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 根据uniqueId查询抖音用户信息
	user, err := dyUserService.GetUserByUniqueId(req.UniqueId)
	if err != nil {
		response.FailWithMessage("获取抖音用户信息失败: "+err.Error(), c)
		return
	}
	// 根据videoId查询视频信息
	video, err := videoService.GetVideo(req.VideoId)
	if err != nil {
		response.FailWithMessage("获取视频信息失败: "+err.Error(), c)
		return
	}

	if video.Status != 0 {
		response.FailWithMessage("该视频处于非待发布状态，不可选用", c)
		return
	}

	// 调用服务上传视频
	record := &creative.AutoPublishVideoRecord{
		DyUserId:        int64(user.ID),
		Weekday:         0,
		Hour:            0,
		Minute:          0,
		Type:            0,
		VideoCategoryId: int(video.CategoryId),
		SysUserId:       user.SysUserId,
		Nickname:        user.Nickname,
		UniqueId:        user.UniqueId,
		TimingType:      1,
	}
	videoLogic := logic.VideoLogic{}
	err = videoLogic.UploadVideo(req.UniqueId, video, record)
	if err != nil {
		response.FailWithMessage("上传视频失败: "+err.Error(), c)
		return
	}

	response.Ok(c)
	return
}
