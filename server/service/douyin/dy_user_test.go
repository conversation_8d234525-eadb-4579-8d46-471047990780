package douyin

import (
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	"github.com/go-redis/redismock/v9"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/suite"
	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// DyUserServiceTestSuite 测试套件
type DyUserServiceTestSuite struct {
	suite.Suite
	service       *DyUserService
	db            *gorm.DB
	sqlMock       sqlmock.Sqlmock
	redisMock     redismock.ClientMock
	originalDB    *gorm.DB
	originalRedis redis.UniversalClient
	originalLog   *zap.Logger
}

// SetupSuite 在所有测试开始前执行
func (suite *DyUserServiceTestSuite) SetupSuite() {
	// 保存原始的全局变量
	suite.originalDB = global.GVA_DB
	suite.originalRedis = global.GVA_REDIS
	suite.originalLog = global.GVA_LOG

	// 创建测试用的 logger
	global.GVA_LOG, _ = zap.NewDevelopment()

	// 创建 SQL mock
	db, mock, err := sqlmock.New()
	suite.Require().NoError(err)
	suite.sqlMock = mock

	// 创建 GORM 实例
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	suite.Require().NoError(err)
	suite.db = gormDB
	global.GVA_DB = gormDB

	// 创建 Redis mock
	redisClient, redisMock := redismock.NewClientMock()
	suite.redisMock = redisMock
	global.GVA_REDIS = redisClient

	// 创建服务实例
	suite.service = &DyUserService{}
}

// TearDownSuite 在所有测试结束后执行
func (suite *DyUserServiceTestSuite) TearDownSuite() {
	// 恢复原始的全局变量
	global.GVA_DB = suite.originalDB
	global.GVA_REDIS = suite.originalRedis
	global.GVA_LOG = suite.originalLog
}

// SetupTest 在每个测试开始前执行
func (suite *DyUserServiceTestSuite) SetupTest() {
	// 每个测试前不需要特殊处理，mock 会自动管理期望
}

// TearDownTest 在每个测试结束后执行
func (suite *DyUserServiceTestSuite) TearDownTest() {
	// 验证所有 SQL mock 期望都被满足
	err := suite.sqlMock.ExpectationsWereMet()
	suite.NoError(err)
}

// TestAddUser_NewUser 测试添加新用户
func (suite *DyUserServiceTestSuite) TestAddUser_NewUser() {
	// 准备测试数据
	newUser := &douyin.DyUser{
		UID:        "test_uid_123",
		Nickname:   "测试用户",
		UniqueId:   "test_unique_123",
		SysUserId:  1,
		CategoryId: 2,
	}

	// Mock 查询用户不存在
	suite.sqlMock.ExpectQuery("SELECT \\* FROM `dy_user`").
		WithArgs("test_uid_123").
		WillReturnError(gorm.ErrRecordNotFound)

	// Mock 创建新用户
	suite.sqlMock.ExpectBegin()
	suite.sqlMock.ExpectExec("INSERT INTO `dy_user`").
		WillReturnResult(sqlmock.NewResult(123, 1)) // 返回 ID = 123
	suite.sqlMock.ExpectCommit()

	// Mock Redis 操作 - 获取全部分类排序
	suite.redisMock.ExpectGet("user_sort:1:category:0").
		RedisNil()

	// Mock Redis 操作 - 获取特定分类排序
	suite.redisMock.ExpectGet("user_sort:1:category:2").
		RedisNil()

	// 执行测试
	err := suite.service.AddUser(newUser)

	// 验证结果
	suite.NoError(err)
	suite.Equal(uint(123), newUser.ID) // 验证 ID 被正确设置
}

// TestAddUser_ExistingUserDeleted 测试恢复已删除的用户
func (suite *DyUserServiceTestSuite) TestAddUser_ExistingUserDeleted() {
	// 准备测试数据
	newUser := &douyin.DyUser{
		UID:      "test_uid_456",
		Nickname: "更新后的用户名",
		UniqueId: "test_unique_456",
	}

	deletedTime := time.Now()

	// Mock 查询到已删除的用户
	rows := sqlmock.NewRows([]string{"id", "deleted_at", "uid", "nickname"}).
		AddRow(456, deletedTime, "test_uid_456", "原用户名")

	suite.sqlMock.ExpectQuery("SELECT \\* FROM `dy_user`").
		WithArgs("test_uid_456").
		WillReturnRows(rows)

	// Mock 更新操作
	suite.sqlMock.ExpectBegin()
	suite.sqlMock.ExpectExec("UPDATE `dy_user` SET").
		WillReturnResult(sqlmock.NewResult(456, 1))
	suite.sqlMock.ExpectCommit()

	// 执行测试
	err := suite.service.AddUser(newUser)

	// 验证结果
	suite.NoError(err)
}

// TestAddUser_ExistingUserActive 测试更新现有活跃用户
func (suite *DyUserServiceTestSuite) TestAddUser_ExistingUserActive() {
	// 准备测试数据
	newUser := &douyin.DyUser{
		UID:      "test_uid_789",
		Nickname: "更新后的用户名",
		UniqueId: "test_unique_789",
	}

	// Mock 查询到活跃用户
	rows := sqlmock.NewRows([]string{"id", "deleted_at", "uid", "nickname"}).
		AddRow(789, nil, "test_uid_789", "原用户名")

	suite.sqlMock.ExpectQuery("SELECT \\* FROM `dy_user`").
		WithArgs("test_uid_789").
		WillReturnRows(rows)

	// Mock 更新操作
	suite.sqlMock.ExpectBegin()
	suite.sqlMock.ExpectExec("UPDATE `dy_user` SET").
		WillReturnResult(sqlmock.NewResult(789, 1))
	suite.sqlMock.ExpectCommit()

	// 执行测试
	err := suite.service.AddUser(newUser)

	// 验证结果
	suite.NoError(err)
}

// TestAddUser_DatabaseError 测试数据库错误
func (suite *DyUserServiceTestSuite) TestAddUser_DatabaseError() {
	// 准备测试数据
	newUser := &douyin.DyUser{
		UID:      "test_uid_error",
		Nickname: "测试用户",
	}

	// Mock 查询用户不存在
	suite.sqlMock.ExpectQuery("SELECT \\* FROM `dy_user`").
		WithArgs("test_uid_error").
		WillReturnError(gorm.ErrRecordNotFound)

	// Mock 创建用户失败
	suite.sqlMock.ExpectBegin()
	suite.sqlMock.ExpectExec("INSERT INTO `dy_user`").
		WillReturnError(errors.New("database connection failed"))
	suite.sqlMock.ExpectRollback()

	// 执行测试
	err := suite.service.AddUser(newUser)

	// 验证结果
	suite.Error(err)
	suite.Contains(err.Error(), "database connection failed")
}

// TestAddUser_WithSortingSuccess 测试带排序的新用户添加成功
func (suite *DyUserServiceTestSuite) TestAddUser_WithSortingSuccess() {
	// 准备测试数据
	newUser := &douyin.DyUser{
		UID:        "test_uid_sort",
		Nickname:   "排序测试用户",
		SysUserId:  1,
		CategoryId: 3,
	}

	// Mock 查询用户不存在
	suite.sqlMock.ExpectQuery("SELECT \\* FROM `dy_user`").
		WithArgs("test_uid_sort").
		WillReturnError(gorm.ErrRecordNotFound)

	// Mock 创建新用户
	suite.sqlMock.ExpectBegin()
	suite.sqlMock.ExpectExec("INSERT INTO `dy_user`").
		WillReturnResult(sqlmock.NewResult(999, 1))
	suite.sqlMock.ExpectCommit()

	// Mock Redis 操作 - 获取全部分类排序（返回现有排序）
	existingSort := []uint{100, 200, 300}
	existingSortJSON, _ := json.Marshal(existingSort)
	suite.redisMock.ExpectGet("user_sort:1:category:0").
		SetVal(string(existingSortJSON))

	// Mock Redis 操作 - 保存全部分类排序
	expectedSort := []uint{999, 100, 200, 300}
	expectedSortJSON, _ := json.Marshal(expectedSort)
	suite.redisMock.ExpectSet("user_sort:1:category:0", string(expectedSortJSON), 30*24*time.Hour).
		SetVal("OK")

	// Mock Redis 操作 - 获取特定分类排序
	categorySort := []uint{400, 500}
	categorySortJSON, _ := json.Marshal(categorySort)
	suite.redisMock.ExpectGet("user_sort:1:category:3").
		SetVal(string(categorySortJSON))

	// Mock Redis 操作 - 保存特定分类排序
	expectedCategorySort := []uint{999, 400, 500}
	expectedCategorySortJSON, _ := json.Marshal(expectedCategorySort)
	suite.redisMock.ExpectSet("user_sort:1:category:3", string(expectedCategorySortJSON), 30*24*time.Hour).
		SetVal("OK")

	// 执行测试
	err := suite.service.AddUser(newUser)

	// 验证结果
	suite.NoError(err)
	suite.Equal(uint(999), newUser.ID)
}

// TestAddUser_EmptyIDAfterCreate 测试创建后ID为空的情况
func (suite *DyUserServiceTestSuite) TestAddUser_EmptyIDAfterCreate() {
	// 准备测试数据
	newUser := &douyin.DyUser{
		UID:       "test_uid_empty_id",
		Nickname:  "空ID测试用户",
		SysUserId: 1,
	}

	// Mock 查询用户不存在
	suite.sqlMock.ExpectQuery("SELECT \\* FROM `dy_user`").
		WithArgs("test_uid_empty_id").
		WillReturnError(gorm.ErrRecordNotFound)

	// Mock 创建用户但不设置ID（模拟ID为0的情况）
	suite.sqlMock.ExpectBegin()
	suite.sqlMock.ExpectExec("INSERT INTO `dy_user`").
		WillReturnResult(sqlmock.NewResult(0, 1)) // 返回 ID = 0
	suite.sqlMock.ExpectCommit()

	// 执行测试
	err := suite.service.AddUser(newUser)

	// 验证结果
	suite.NoError(err)
	// 验证ID确实为0（这是我们要测试的bug场景）
	suite.Equal(uint(0), newUser.ID)
}

// TestAddUser_RedisError 测试Redis操作失败的情况
func (suite *DyUserServiceTestSuite) TestAddUser_RedisError() {
	// 准备测试数据
	newUser := &douyin.DyUser{
		UID:       "test_uid_redis_error",
		Nickname:  "Redis错误测试",
		SysUserId: 1,
	}

	// Mock 查询用户不存在
	suite.sqlMock.ExpectQuery("SELECT \\* FROM `dy_user`").
		WithArgs("test_uid_redis_error").
		WillReturnError(gorm.ErrRecordNotFound)

	// Mock 创建新用户
	suite.sqlMock.ExpectBegin()
	suite.sqlMock.ExpectExec("INSERT INTO `dy_user`").
		WillReturnResult(sqlmock.NewResult(888, 1))
	suite.sqlMock.ExpectCommit()

	// Mock Redis 操作失败
	suite.redisMock.ExpectGet("user_sort:1:category:0").
		SetErr(errors.New("redis connection failed"))

	// 执行测试
	err := suite.service.AddUser(newUser)

	// 验证结果 - 即使Redis失败，用户创建应该成功
	suite.NoError(err)
	suite.Equal(uint(888), newUser.ID)
}

// TestAddUser_NoSysUserId 测试没有系统用户ID的情况
func (suite *DyUserServiceTestSuite) TestAddUser_NoSysUserId() {
	// 准备测试数据
	newUser := &douyin.DyUser{
		UID:       "test_uid_no_sys_user",
		Nickname:  "无系统用户ID测试",
		SysUserId: 0, // 没有系统用户ID
	}

	// Mock 查询用户不存在
	suite.sqlMock.ExpectQuery("SELECT \\* FROM `dy_user`").
		WithArgs("test_uid_no_sys_user").
		WillReturnError(gorm.ErrRecordNotFound)

	// Mock 创建新用户
	suite.sqlMock.ExpectBegin()
	suite.sqlMock.ExpectExec("INSERT INTO `dy_user`").
		WillReturnResult(sqlmock.NewResult(777, 1))
	suite.sqlMock.ExpectCommit()

	// 执行测试
	err := suite.service.AddUser(newUser)

	// 验证结果 - 应该成功，但不会进行排序操作
	suite.NoError(err)
	suite.Equal(uint(777), newUser.ID)
}

// TestAddUser_NilUser 测试传入nil用户的情况
func (suite *DyUserServiceTestSuite) TestAddUser_NilUser() {
	// 执行测试
	err := suite.service.AddUser(nil)

	// 验证结果 - 应该出错
	suite.Error(err)
}

// TestAddUser_EmptyUID 测试空UID的情况
func (suite *DyUserServiceTestSuite) TestAddUser_EmptyUID() {
	// 准备测试数据
	newUser := &douyin.DyUser{
		UID:      "", // 空UID
		Nickname: "空UID测试",
	}

	// Mock 查询空UID
	suite.sqlMock.ExpectQuery("SELECT \\* FROM `dy_user`").
		WithArgs("").
		WillReturnError(gorm.ErrRecordNotFound)

	// Mock 创建用户
	suite.sqlMock.ExpectBegin()
	suite.sqlMock.ExpectExec("INSERT INTO `dy_user`").
		WillReturnResult(sqlmock.NewResult(666, 1))
	suite.sqlMock.ExpectCommit()

	// 执行测试
	err := suite.service.AddUser(newUser)

	// 验证结果
	suite.NoError(err)
	suite.Equal(uint(666), newUser.ID)
}

// TestAddUser_LargeUserData 测试大量用户数据的情况
func (suite *DyUserServiceTestSuite) TestAddUser_LargeUserData() {
	// 准备测试数据 - 包含所有字段
	newUser := &douyin.DyUser{
		UID:                "test_uid_large_data",
		Nickname:           "大数据测试用户",
		Avatar:             "https://example.com/avatar.jpg",
		UniqueId:           "large_data_unique_123",
		ShortId:            "short123",
		SecUid:             "sec_uid_123456789",
		FollowerCount:      1000000,
		FollowingCount:     500,
		AwemeCount:         200,
		TotalFavorited:     5000000,
		AccountRegion:      "中国",
		Province:           "北京市",
		City:               "北京市",
		CollegeName:        "清华大学",
		BindPhone:          "***********",
		Birthday:           "1990-01-01",
		Gender:             1,
		Signature:          "这是一个很长的个性签名，包含各种特殊字符!@#$%^&*()",
		SysUserId:          1,
		IsProductEnabled:   true,
		BindIP:             "*************",
		BindDevice:         `{"id":1,"name":"iPhone 13"}`,
		Did:                "device_id_123",
		Iid:                "instance_id_456",
		Status:             1,
		WithdrawableAmount: 10000,
		Day1TotalAmount:    500,
		Day7TotalAmount:    3500,
		TotalAmount:        50000,
		RealName:           "张三",
		AccountType:        1,
		AutoPublishStatus:  1,
		TalkAuthStatus:     1,
		BiteBrowserId:      "bite_browser_123",
		Remark:             "测试备注信息",
		CategoryId:         5,
	}

	// Mock 查询用户不存在
	suite.sqlMock.ExpectQuery("SELECT \\* FROM `dy_user`").
		WithArgs("test_uid_large_data").
		WillReturnError(gorm.ErrRecordNotFound)

	// Mock 创建新用户
	suite.sqlMock.ExpectBegin()
	suite.sqlMock.ExpectExec("INSERT INTO `dy_user`").
		WillReturnResult(sqlmock.NewResult(555, 1))
	suite.sqlMock.ExpectCommit()

	// Mock Redis 操作
	suite.redisMock.ExpectGet("user_sort:1:category:0").RedisNil()
	suite.redisMock.ExpectGet("user_sort:1:category:5").RedisNil()

	// 执行测试
	err := suite.service.AddUser(newUser)

	// 验证结果
	suite.NoError(err)
	suite.Equal(uint(555), newUser.ID)
}

// 运行测试套件
func TestDyUserServiceSuite(t *testing.T) {
	suite.Run(t, new(DyUserServiceTestSuite))
}

// TestAddUser_Integration 集成测试示例（需要真实数据库）
func TestAddUser_Integration(t *testing.T) {
	// 跳过集成测试，除非设置了特定的环境变量
	if testing.Short() {
		t.Skip("跳过集成测试")
	}

	// 这里可以添加真实的数据库集成测试
	// 需要设置测试数据库连接
	t.Log("集成测试需要真实的数据库连接")
}
