package douyin

import (
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	"github.com/go-redis/redismock/v9"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// setupTestEnvironment 设置测试环境
func setupTestEnvironment(t *testing.T) (*DyUserService, sqlmock.Sqlmock, redismock.ClientMock, func()) {
	// 保存原始的全局变量
	originalDB := global.GVA_DB
	originalRedis := global.GVA_REDIS
	originalLog := global.GVA_LOG

	// 创建测试用的 logger
	global.GVA_LOG, _ = zap.NewDevelopment()

	// 创建 SQL mock
	db, sqlMock, err := sqlmock.New()
	assert.NoError(t, err)

	// 创建 GORM 实例
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	assert.NoError(t, err)
	global.GVA_DB = gormDB

	// 创建 Redis mock
	redisClient, redisMock := redismock.NewClientMock()
	global.GVA_REDIS = redisClient

	// 创建服务实例
	service := &DyUserService{}

	// 返回清理函数
	cleanup := func() {
		// 验证所有期望都被满足
		err := sqlMock.ExpectationsWereMet()
		assert.NoError(t, err)

		// 恢复原始的全局变量
		global.GVA_DB = originalDB
		global.GVA_REDIS = originalRedis
		global.GVA_LOG = originalLog
	}

	return service, sqlMock, redisMock, cleanup
}

// TestAddUser_NewUserSuccess 测试成功添加新用户
func TestAddUser_NewUserSuccess(t *testing.T) {
	service, sqlMock, redisMock, cleanup := setupTestEnvironment(t)
	defer cleanup()

	// 准备测试数据
	newUser := &douyin.DyUser{
		UID:        "test_uid_123",
		Nickname:   "测试用户",
		UniqueId:   "test_unique_123",
		SysUserId:  1,
		CategoryId: 2,
	}

	// Mock 查询用户不存在
	sqlMock.ExpectQuery("SELECT \\* FROM `dy_user`").
		WithArgs("test_uid_123").
		WillReturnError(gorm.ErrRecordNotFound)

	// Mock 创建新用户
	sqlMock.ExpectBegin()
	sqlMock.ExpectExec("INSERT INTO `dy_user`").
		WillReturnResult(sqlmock.NewResult(123, 1))
	sqlMock.ExpectCommit()

	// Mock Redis 操作 - 获取全部分类排序
	redisMock.ExpectGet("user_sort:1:category:0").
		RedisNil()

	// Mock Redis 操作 - 获取特定分类排序
	redisMock.ExpectGet("user_sort:1:category:2").
		RedisNil()

	// 执行测试
	err := service.AddUser(newUser)

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, uint(123), newUser.ID)
}

// TestAddUser_ExistingUserUpdate 测试更新现有用户
func TestAddUser_ExistingUserUpdate(t *testing.T) {
	service, sqlMock, _, cleanup := setupTestEnvironment(t)
	defer cleanup()

	// 准备测试数据
	newUser := &douyin.DyUser{
		UID:      "test_uid_456",
		Nickname: "更新后的用户名",
		UniqueId: "test_unique_456",
	}

	// Mock 查询到活跃用户
	rows := sqlmock.NewRows([]string{"id", "deleted_at", "uid", "nickname"}).
		AddRow(456, nil, "test_uid_456", "原用户名")

	sqlMock.ExpectQuery("SELECT \\* FROM `dy_user`").
		WithArgs("test_uid_456").
		WillReturnRows(rows)

	// Mock 更新操作
	sqlMock.ExpectBegin()
	sqlMock.ExpectExec("UPDATE `dy_user` SET").
		WillReturnResult(sqlmock.NewResult(456, 1))
	sqlMock.ExpectCommit()

	// 执行测试
	err := service.AddUser(newUser)

	// 验证结果
	assert.NoError(t, err)
}

// TestAddUser_DatabaseError 测试数据库错误
func TestAddUser_DatabaseError(t *testing.T) {
	service, sqlMock, _, cleanup := setupTestEnvironment(t)
	defer cleanup()

	// 准备测试数据
	newUser := &douyin.DyUser{
		UID:      "test_uid_error",
		Nickname: "测试用户",
	}

	// Mock 查询用户不存在
	sqlMock.ExpectQuery("SELECT \\* FROM `dy_user`").
		WithArgs("test_uid_error").
		WillReturnError(gorm.ErrRecordNotFound)

	// Mock 创建用户失败
	sqlMock.ExpectBegin()
	sqlMock.ExpectExec("INSERT INTO `dy_user`").
		WillReturnError(errors.New("database connection failed"))
	sqlMock.ExpectRollback()

	// 执行测试
	err := service.AddUser(newUser)

	// 验证结果
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "database connection failed")
}

// TestAddUser_WithSorting 测试带排序的用户添加
func TestAddUser_WithSorting(t *testing.T) {
	service, sqlMock, redisMock, cleanup := setupTestEnvironment(t)
	defer cleanup()

	// 准备测试数据
	newUser := &douyin.DyUser{
		UID:        "test_uid_sort",
		Nickname:   "排序测试用户",
		SysUserId:  1,
		CategoryId: 3,
	}

	// Mock 查询用户不存在
	sqlMock.ExpectQuery("SELECT \\* FROM `dy_user`").
		WithArgs("test_uid_sort").
		WillReturnError(gorm.ErrRecordNotFound)

	// Mock 创建新用户
	sqlMock.ExpectBegin()
	sqlMock.ExpectExec("INSERT INTO `dy_user`").
		WillReturnResult(sqlmock.NewResult(999, 1))
	sqlMock.ExpectCommit()

	// Mock Redis 操作 - 获取全部分类排序（返回现有排序）
	existingSort := []uint{100, 200, 300}
	existingSortJSON, _ := json.Marshal(existingSort)
	redisMock.ExpectGet("user_sort:1:category:0").
		SetVal(string(existingSortJSON))

	// Mock Redis 操作 - 保存全部分类排序
	expectedSort := []uint{999, 100, 200, 300}
	expectedSortJSON, _ := json.Marshal(expectedSort)
	redisMock.ExpectSet("user_sort:1:category:0", string(expectedSortJSON), 30*24*time.Hour).
		SetVal("OK")

	// Mock Redis 操作 - 获取特定分类排序
	categorySort := []uint{400, 500}
	categorySortJSON, _ := json.Marshal(categorySort)
	redisMock.ExpectGet("user_sort:1:category:3").
		SetVal(string(categorySortJSON))

	// Mock Redis 操作 - 保存特定分类排序
	expectedCategorySort := []uint{999, 400, 500}
	expectedCategorySortJSON, _ := json.Marshal(expectedCategorySort)
	redisMock.ExpectSet("user_sort:1:category:3", string(expectedCategorySortJSON), 30*24*time.Hour).
		SetVal("OK")

	// 执行测试
	err := service.AddUser(newUser)

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, uint(999), newUser.ID)
}

// TestAddUser_EmptyIDAfterCreate 测试创建后ID为空的情况（这是我们发现的bug）
func TestAddUser_EmptyIDAfterCreate(t *testing.T) {
	service, sqlMock, _, cleanup := setupTestEnvironment(t)
	defer cleanup()

	// 准备测试数据
	newUser := &douyin.DyUser{
		UID:       "test_uid_empty_id",
		Nickname:  "空ID测试用户",
		SysUserId: 1,
	}

	// Mock 查询用户不存在
	sqlMock.ExpectQuery("SELECT \\* FROM `dy_user`").
		WithArgs("test_uid_empty_id").
		WillReturnError(gorm.ErrRecordNotFound)

	// Mock 创建用户但不设置ID（模拟ID为0的情况）
	sqlMock.ExpectBegin()
	sqlMock.ExpectExec("INSERT INTO `dy_user`").
		WillReturnResult(sqlmock.NewResult(0, 1)) // 返回 ID = 0，模拟bug场景
	sqlMock.ExpectCommit()

	// 执行测试
	err := service.AddUser(newUser)

	// 验证结果
	assert.NoError(t, err)
	// 验证ID确实为0（这是我们要测试的bug场景）
	assert.Equal(t, uint(0), newUser.ID)

	// 这个测试展示了bug：如果ID为0，排序功能会将0添加到排序列表中
	// 在实际应用中，这会导致排序列表包含无效的用户ID
}

// TestAddUser_NilUser 测试传入nil用户的情况
func TestAddUser_NilUser(t *testing.T) {
	service, _, _, cleanup := setupTestEnvironment(t)
	defer cleanup()

	// 执行测试 - 这应该会panic，因为代码没有检查nil
	assert.Panics(t, func() {
		service.AddUser(nil)
	})
}

// TestAddUser_NoSysUserId 测试没有系统用户ID的情况
func TestAddUser_NoSysUserId(t *testing.T) {
	service, sqlMock, _, cleanup := setupTestEnvironment(t)
	defer cleanup()

	// 准备测试数据
	newUser := &douyin.DyUser{
		UID:       "test_uid_no_sys_user",
		Nickname:  "无系统用户ID测试",
		SysUserId: 0, // 没有系统用户ID
	}

	// Mock 查询用户不存在
	sqlMock.ExpectQuery("SELECT \\* FROM `dy_user`").
		WithArgs("test_uid_no_sys_user").
		WillReturnError(gorm.ErrRecordNotFound)

	// Mock 创建新用户
	sqlMock.ExpectBegin()
	sqlMock.ExpectExec("INSERT INTO `dy_user`").
		WillReturnResult(sqlmock.NewResult(777, 1))
	sqlMock.ExpectCommit()

	// 执行测试
	err := service.AddUser(newUser)

	// 验证结果 - 应该成功，但不会进行排序操作
	assert.NoError(t, err)
	assert.Equal(t, uint(777), newUser.ID)
}
