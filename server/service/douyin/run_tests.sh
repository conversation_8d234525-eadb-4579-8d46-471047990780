#!/bin/bash

# AddUser 函数单元测试运行脚本
# 用于测试 server/service/douyin/dy_user.go 中的 AddUser 函数

echo "🧪 运行 AddUser 函数单元测试"
echo "================================"

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 进入正确的目录
cd "$(dirname "$0")"

echo -e "${BLUE}📍 当前目录: $(pwd)${NC}"
echo ""

# 1. 运行 Bug 验证测试
echo -e "${YELLOW}1. 运行 Bug 验证测试${NC}"
echo "测试目的：验证 dyUser.ID 为空的 bug"
echo "----------------------------------------"
go test -v -run "TestAddUser_IDValidation|TestSortingLogic|TestBugDocumentation"
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Bug 验证测试通过${NC}"
else
    echo -e "${RED}❌ Bug 验证测试失败${NC}"
    exit 1
fi
echo ""

# 2. 运行修复验证测试
echo -e "${YELLOW}2. 运行修复验证测试${NC}"
echo "测试目的：验证修复方案的有效性"
echo "----------------------------------------"
go test -v -run "TestFixedSortingLogic"
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 修复验证测试通过${NC}"
else
    echo -e "${RED}❌ 修复验证测试失败${NC}"
    exit 1
fi
echo ""

# 3. 运行场景测试
echo -e "${YELLOW}3. 运行场景测试${NC}"
echo "测试目的：测试各种用户创建场景"
echo "----------------------------------------"
go test -v -run "TestUserCreationScenarios"
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 场景测试通过${NC}"
else
    echo -e "${RED}❌ 场景测试失败${NC}"
    exit 1
fi
echo ""

# 4. 运行所有相关测试
echo -e "${YELLOW}4. 运行所有 AddUser 相关测试${NC}"
echo "测试目的：完整的测试覆盖"
echo "----------------------------------------"
go test -v -run "TestAddUser_"
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 所有测试通过${NC}"
else
    echo -e "${RED}❌ 部分测试失败${NC}"
    echo -e "${YELLOW}注意：某些测试可能需要数据库连接，失败是正常的${NC}"
fi
echo ""

# 5. 生成测试覆盖率报告
echo -e "${YELLOW}5. 生成测试覆盖率报告${NC}"
echo "----------------------------------------"
go test -coverprofile=coverage.out -run "TestAddUser_IDValidation|TestSortingLogic|TestFixedSortingLogic|TestUserCreationScenarios|TestBugDocumentation"
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 覆盖率报告生成成功${NC}"
    go tool cover -html=coverage.out -o coverage.html
    echo -e "${BLUE}📊 覆盖率报告已保存到 coverage.html${NC}"
else
    echo -e "${RED}❌ 覆盖率报告生成失败${NC}"
fi
echo ""

# 总结
echo -e "${BLUE}📋 测试总结${NC}"
echo "================================"
echo "✅ Bug 验证：确认了 dyUser.ID 可能为空的问题"
echo "✅ 影响分析：会导致排序列表包含无效 ID"
echo "✅ 修复方案：在排序前检查 ID 有效性"
echo "✅ 测试覆盖：包含正常场景、边界情况和错误处理"
echo ""
echo -e "${GREEN}🎉 测试完成！${NC}"
echo ""
echo -e "${YELLOW}📝 下一步建议：${NC}"
echo "1. 在实际代码中实现 ID 验证逻辑"
echo "2. 添加日志记录以便调试"
echo "3. 考虑返回错误而不是静默跳过"
echo "4. 在生产环境部署前进行集成测试"
