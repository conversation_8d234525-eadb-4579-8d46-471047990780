
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>douyin: Go Coverage Report</title>
		<style>
			body {
				background: black;
				color: rgb(80, 80, 80);
			}
			body, pre, #legend span {
				font-family: Menlo, monospace;
				font-weight: bold;
			}
			#topbar {
				background: black;
				position: fixed;
				top: 0; left: 0; right: 0;
				height: 42px;
				border-bottom: 1px solid rgb(80, 80, 80);
			}
			#content {
				margin-top: 50px;
			}
			#nav, #legend {
				float: left;
				margin-left: 10px;
			}
			#legend {
				margin-top: 12px;
			}
			#nav {
				margin-top: 10px;
			}
			#legend span {
				margin: 0 5px;
			}
			.cov0 { color: rgb(192, 0, 0) }
.cov1 { color: rgb(128, 128, 128) }
.cov2 { color: rgb(116, 140, 131) }
.cov3 { color: rgb(104, 152, 134) }
.cov4 { color: rgb(92, 164, 137) }
.cov5 { color: rgb(80, 176, 140) }
.cov6 { color: rgb(68, 188, 143) }
.cov7 { color: rgb(56, 200, 146) }
.cov8 { color: rgb(44, 212, 149) }
.cov9 { color: rgb(32, 224, 152) }
.cov10 { color: rgb(20, 236, 155) }

		</style>
	</head>
	<body>
		<div id="topbar">
			<div id="nav">
				<select id="files">
				
				<option value="file0">github.com/flipped-aurora/gin-vue-admin/server/service/douyin/aweme_api.go (0.0%)</option>
				
				<option value="file1">github.com/flipped-aurora/gin-vue-admin/server/service/douyin/device_info.go (0.0%)</option>
				
				<option value="file2">github.com/flipped-aurora/gin-vue-admin/server/service/douyin/dy_auth_user.go (0.0%)</option>
				
				<option value="file3">github.com/flipped-aurora/gin-vue-admin/server/service/douyin/dy_aweme.go (0.0%)</option>
				
				<option value="file4">github.com/flipped-aurora/gin-vue-admin/server/service/douyin/dy_chat_contact.go (0.0%)</option>
				
				<option value="file5">github.com/flipped-aurora/gin-vue-admin/server/service/douyin/dy_chat_contact_user.go (0.0%)</option>
				
				<option value="file6">github.com/flipped-aurora/gin-vue-admin/server/service/douyin/dy_http_client.go (0.0%)</option>
				
				<option value="file7">github.com/flipped-aurora/gin-vue-admin/server/service/douyin/dy_product.go (0.0%)</option>
				
				<option value="file8">github.com/flipped-aurora/gin-vue-admin/server/service/douyin/dy_product_manual.go (0.0%)</option>
				
				<option value="file9">github.com/flipped-aurora/gin-vue-admin/server/service/douyin/dy_product_selection.go (0.0%)</option>
				
				<option value="file10">github.com/flipped-aurora/gin-vue-admin/server/service/douyin/dy_top_comment.go (0.0%)</option>
				
				<option value="file11">github.com/flipped-aurora/gin-vue-admin/server/service/douyin/dy_user.go (0.0%)</option>
				
				<option value="file12">github.com/flipped-aurora/gin-vue-admin/server/service/douyin/dy_user_category.go (0.0%)</option>
				
				<option value="file13">github.com/flipped-aurora/gin-vue-admin/server/service/douyin/flame_api.go (0.0%)</option>
				
				<option value="file14">github.com/flipped-aurora/gin-vue-admin/server/service/douyin/flame_player.go (0.0%)</option>
				
				<option value="file15">github.com/flipped-aurora/gin-vue-admin/server/service/douyin/flame_player_collection.go (0.0%)</option>
				
				<option value="file16">github.com/flipped-aurora/gin-vue-admin/server/service/douyin/flame_user.go (0.0%)</option>
				
				<option value="file17">github.com/flipped-aurora/gin-vue-admin/server/service/douyin/ip.go (0.0%)</option>
				
				<option value="file18">github.com/flipped-aurora/gin-vue-admin/server/service/douyin/more_api.go (0.0%)</option>
				
				<option value="file19">github.com/flipped-aurora/gin-vue-admin/server/service/douyin/more_creator_advance_api.go (0.0%)</option>
				
				<option value="file20">github.com/flipped-aurora/gin-vue-admin/server/service/douyin/more_creator_api.go (0.0%)</option>
				
				<option value="file21">github.com/flipped-aurora/gin-vue-admin/server/service/douyin/more_creator_custom_api.go (0.0%)</option>
				
				<option value="file22">github.com/flipped-aurora/gin-vue-admin/server/service/douyin/more_message_api.go (0.0%)</option>
				
				<option value="file23">github.com/flipped-aurora/gin-vue-admin/server/service/douyin/phone_balance.go (0.0%)</option>
				
				</select>
			</div>
			<div id="legend">
				<span>not tracked</span>
			
				<span class="cov0">not covered</span>
				<span class="cov8">covered</span>
			
			</div>
		</div>
		<div id="content">
		
		<pre class="file" id="file0" style="display: none">package douyin

import (
        "encoding/json"
        "fmt"
        "sync"

        "github.com/flipped-aurora/gin-vue-admin/server/global"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/response"
)

type AwemeApiService struct {
        client *DyHttpClient
        once   sync.Once
}

var AwemeApiServiceApp = new(AwemeApiService)

func (s *AwemeApiService) getClient() *DyHttpClient <span class="cov0" title="0">{
        s.once.Do(func() </span><span class="cov0" title="0">{
                s.client = NewDyHttpClient(global.GVA_CONFIG.Douyin.BaseURL)
        }</span>)
        <span class="cov0" title="0">return s.client</span>
}

func (s *AwemeApiService) updateReq(req map[string]interface{}) error <span class="cov0" title="0">{
        // 获取用户信息，检查是否配置了IP
        dyUser, err := DyUserServiceApp.GetUserByToken(req["token"].(string))
        if err != nil </span><span class="cov0" title="0">{
                return err
        }</span>
        <span class="cov0" title="0">if dyUser.BindIP == "" </span><span class="cov0" title="0">{
                return fmt.Errorf("用户未配置IP地址，请先在用户管理中配置IP")
        }</span>
        <span class="cov0" title="0">req["proxy_ip"] = dyUser.BindIP
        req["device_id"] = dyUser.Did
        return nil</span>
}

// GetUserInfo 获取抖音用户信息
func (s *AwemeApiService) GetUserInfo(token, ip string) (response.UserInfoResponse, error) <span class="cov0" title="0">{
        reqBody := map[string]interface{}{
                "token":    token,
                "proxy_ip": ip,
        }

        var userInfo response.UserInfoResponse
        err := s.getClient().DoRequest("POST", "/api/douyin/user_info", reqBody, &amp;userInfo)
        if err != nil </span><span class="cov0" title="0">{
                return response.UserInfoResponse{}, err
        }</span>
        <span class="cov0" title="0">return userInfo, nil</span>
}

// GetProductList 获取精选商品Feed流
func (s *AwemeApiService) GetProductList(req request.ProductListRequest) (response.ProductListResponse, error) <span class="cov0" title="0">{
        reqMap := map[string]interface{}{
                "token":   req.Token,
                "cursor":  req.Cursor,
                "filters": req.Filters,
        }
        if err := s.updateReq(reqMap); err != nil </span><span class="cov0" title="0">{
                return response.ProductListResponse{}, err
        }</span>
        <span class="cov0" title="0">var productList response.ProductListResponse
        if err := s.getClient().DoRequest("POST", "/api/douyin/material_list", reqMap, &amp;productList); err != nil </span><span class="cov0" title="0">{
                return response.ProductListResponse{}, err
        }</span>
        <span class="cov0" title="0">return productList, nil</span>
}

// GetPackDetail 获取商品详细信息和推广数据
func (s *AwemeApiService) GetPackDetail(req request.PackDetailRequest) (response.PackDetailResponse, error) <span class="cov0" title="0">{
        // 确保data_module为non-core
        req.DataModule = "non-core"

        // 将请求转换为map以添加proxy_ip
        reqMap := map[string]interface{}{
                "token":       req.Token,
                "data_module": req.DataModule,
                "product_id":  req.ProductID,
        }
        if err := s.updateReq(reqMap); err != nil </span><span class="cov0" title="0">{
                return response.PackDetailResponse{}, err
        }</span>

        <span class="cov0" title="0">var packDetail response.PackDetailResponse
        if err := s.getClient().DoRequest("POST", "/api/douyin/pack_detail", reqMap, &amp;packDetail); err != nil </span><span class="cov0" title="0">{
                return response.PackDetailResponse{}, err
        }</span>
        <span class="cov0" title="0">return packDetail, nil</span>
}

// GetAuthorList 获取带货达人榜单
func (s *AwemeApiService) GetAuthorList(req request.AuthorListRequest) (response.AuthorListResponse, error) <span class="cov0" title="0">{
        // 确保data_module为dynamic
        req.DataModule = "dynamic"

        // 将请求转换为map以添加proxy_ip
        reqBytes, err := json.Marshal(req)
        if err != nil </span><span class="cov0" title="0">{
                return response.AuthorListResponse{}, err
        }</span>

        <span class="cov0" title="0">var reqMap map[string]interface{}
        if err := json.Unmarshal(reqBytes, &amp;reqMap); err != nil </span><span class="cov0" title="0">{
                return response.AuthorListResponse{}, err
        }</span>

        <span class="cov0" title="0">if err := s.updateReq(reqMap); err != nil </span><span class="cov0" title="0">{
                return response.AuthorListResponse{}, err
        }</span>

        <span class="cov0" title="0">var authorList response.AuthorListResponse
        err = s.getClient().DoRequest("POST", "/api/douyin/pack_detail", reqMap, &amp;authorList)
        if err != nil </span><span class="cov0" title="0">{
                return response.AuthorListResponse{}, err
        }</span>
        <span class="cov0" title="0">return authorList, nil</span>
}
</pre>
		
		<pre class="file" id="file1" style="display: none">package douyin

import (
        "fmt"
        "math/rand"

        "github.com/flipped-aurora/gin-vue-admin/server/global"
        "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
        douyinRequest "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/response"
)

type DeviceInfoService struct{}

// CreateDeviceInfo 创建设备信息
func (s *DeviceInfoService) CreateDeviceInfo(req douyinRequest.DeviceInfoRequest) (resp response.DeviceInfoResponse, err error) <span class="cov0" title="0">{
        deviceInfo := douyin.DeviceInfo{
                DeviceID:         req.DeviceID,
                InstallID:        req.InstallID,
                KlinkEgdi:        req.KlinkEgdi,
                NewUser:          req.NewUser,
                DeviceIDStr:      req.DeviceIDStr,
                InstallIDStr:     req.InstallIDStr,
                DeviceToken:      req.DeviceToken,
                DtraitPk1:        req.DtraitPk1,
                DtraitPk2:        req.DtraitPk2,
                DtraitPk1Version: req.DtraitPk1Version,
                DtraitPk2Version: req.DtraitPk2Version,
                DtraitVersion:    req.DtraitVersion,
                ServerTime:       req.ServerTime,
                DyUserID:         req.DyUserID,
        }

        err = global.GVA_DB.Create(&amp;deviceInfo).Error
        if err != nil </span><span class="cov0" title="0">{
                return response.DeviceInfoResponse{
                        Success: false,
                        Message: "创建设备信息失败",
                }, err
        }</span>

        <span class="cov0" title="0">return response.DeviceInfoResponse{
                Success: true,
                Message: "创建设备信息成功",
                ID:      deviceInfo.ID,
        }, nil</span>
}

// GetDeviceInfoList 获取设备信息列表
func (s *DeviceInfoService) GetDeviceInfoList(info request.PageInfo) (list interface{}, total int64, err error) <span class="cov0" title="0">{
        limit := info.PageSize
        offset := info.PageSize * (info.Page - 1)
        db := global.GVA_DB.Model(&amp;douyin.DeviceInfo{})
        var deviceInfoList []struct {
                douyin.DeviceInfo
                UserNickname      string `json:"userNickname"`      // 抖音用户昵称
                FlameUserNickname string `json:"flameUserNickname"` // 火山版用户昵称
        }

        err = db.Count(&amp;total).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, 0, err
        }</span>

        // 使用左连接查询设备信息、抖音用户信息和火山版用户信息
        <span class="cov0" title="0">err = db.Select("device_info.*, dy_user.nickname as user_nickname, flame_user.nickname as flame_user_nickname").
                Joins("LEFT JOIN dy_user ON device_info.dy_user_id = dy_user.id").
                Joins("LEFT JOIN flame_user ON device_info.flame_user_id = flame_user.id").
                Where("device_info.deleted_at IS NULL").
                Order("device_info.created_at DESC").
                Limit(limit).Offset(offset).
                Scan(&amp;deviceInfoList).Error

        return deviceInfoList, total, err</span>
}

// GetAvailableDevices 获取可用的设备列表（未绑定给用户的设备）
func (s *DeviceInfoService) GetAvailableDevices() ([]douyin.DeviceInfo, error) <span class="cov0" title="0">{
        var devices []douyin.DeviceInfo

        // 查询dy_user_id为NULL的设备
        err := global.GVA_DB.Where("dy_user_id IS NULL").
                Where("deleted_at IS NULL").
                Find(&amp;devices).Error

        return devices, err
}</span>

// GetRandomDevice 随机获取一个可用设备
func (s *DeviceInfoService) GetRandomDevice() (map[string]interface{}, error) <span class="cov0" title="0">{
        devices, err := s.GetAvailableDevices()
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        <span class="cov0" title="0">if len(devices) == 0 </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("没有可用的设备")
        }</span>

        // 随机选择一个设备
        <span class="cov0" title="0">randomIndex := rand.Intn(len(devices))
        device := devices[randomIndex]

        // 返回设备信息
        deviceInfo := map[string]interface{}{
                "id":         device.ID,
                "device_id":  device.DeviceIDStr,
                "install_id": device.InstallIDStr,
        }

        return deviceInfo, nil</span>
}

// UpdateDeviceUser 更新设备绑定的用户ID
func (s *DeviceInfoService) UpdateDeviceUser(deviceID uint, dyUserID uint) error <span class="cov0" title="0">{
        return global.GVA_DB.Model(&amp;douyin.DeviceInfo{}).
                Where("id = ?", deviceID).
                Update("dy_user_id", dyUserID).Error
}</span>
</pre>
		
		<pre class="file" id="file2" style="display: none">package douyin

import (
        "encoding/json"
        "errors"
        "fmt"
        "io/ioutil"
        "net/http"
        "net/url"
        "strconv"
        "strings"
        "time"

        "github.com/flipped-aurora/gin-vue-admin/server/global"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/response"
)

type DyAuthUserService struct{}

// 抖音开放平台配置
const (
        ClientKey    = "aw1he62ush8njlki"    // 替换为您的抖音开放平台应用的ClientKey
        ClientSecret = "fd3d1ed6bad915e19a6040c73b4ecebe" // 替换为您的抖音开放平台应用的ClientSecret
        Scope        = "trial.whitelist"          // 授权范围，可根据需要修改
)

// GetAuthUrl 获取抖音授权URL
func (s *DyAuthUserService) GetAuthUrl(categoryId uint, userId int64, redirectUri string) (string, error) <span class="cov0" title="0">{
        // 构建state参数，格式为：categoryId_userId
        state := fmt.Sprintf("%d_%d", categoryId, userId)

        // 构建授权URL
        authUrl := fmt.Sprintf(
                "https://open.douyin.com/platform/oauth/connect/?client_key=%s&amp;response_type=code&amp;scope=%s&amp;redirect_uri=%s&amp;state=%s",
                ClientKey,
                Scope,
                url.QueryEscape(redirectUri),
                url.QueryEscape(state),
        )

        return authUrl, nil
}</span>

// ParseStateParams 解析state参数
func (s *DyAuthUserService) ParseStateParams(state string) (*request.StateParams, error) <span class="cov0" title="0">{
        parts := strings.Split(state, "_")
        if len(parts) &lt; 2 </span><span class="cov0" title="0">{
                return nil, errors.New("无效的state参数格式")
        }</span>

        <span class="cov0" title="0">categoryId, err := strconv.ParseUint(parts[0], 10, 64)
        if err != nil </span><span class="cov0" title="0">{
                return nil, errors.New("无效的分类ID")
        }</span>

        <span class="cov0" title="0">userId, err := strconv.ParseInt(parts[1], 10, 64)
        if err != nil </span><span class="cov0" title="0">{
                return nil, errors.New("无效的用户ID")
        }</span>

        <span class="cov0" title="0">return &amp;request.StateParams{
                CategoryId: uint(categoryId),
                UserId:     userId,
        }, nil</span>
}

// GetAccessToken 获取访问令牌
func (s *DyAuthUserService) GetAccessToken(code string) (*response.DyTokenResponse, error) <span class="cov0" title="0">{
        // 构建请求参数
        data := url.Values{}
        data.Set("client_key", ClientKey)
        data.Set("client_secret", ClientSecret)
        data.Set("code", code)
        data.Set("grant_type", "authorization_code")

        // 发送请求
        resp, err := http.PostForm("https://open.douyin.com/oauth/access_token/", data)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>
        <span class="cov0" title="0">defer resp.Body.Close()

        // 读取响应
        body, err := ioutil.ReadAll(resp.Body)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        // 解析响应
        <span class="cov0" title="0">var result struct {
                Message string                   `json:"message"`
                Data    response.DyTokenResponse `json:"data"`
        }
        if err := json.Unmarshal(body, &amp;result); err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        <span class="cov0" title="0">if result.Data.AccessToken == "" </span><span class="cov0" title="0">{
                return nil, errors.New("获取访问令牌失败: " + result.Message)
        }</span>

        <span class="cov0" title="0">return &amp;result.Data, nil</span>
}

// GetUserInfoByAccessToken 通过访问令牌获取用户信息
func (s *DyAuthUserService) GetUserInfoByAccessToken(accessToken, openId string) (*response.DyUserInfoResponse, error) <span class="cov0" title="0">{
        // 构建请求URL
        apiUrl := fmt.Sprintf(
                "https://open.douyin.com/oauth/userinfo/?access_token=%s&amp;open_id=%s",
                accessToken,
                openId,
        )

        // 发送请求
        resp, err := http.Get(apiUrl)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>
        <span class="cov0" title="0">defer resp.Body.Close()

        // 读取响应
        body, err := ioutil.ReadAll(resp.Body)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        // 解析响应
        <span class="cov0" title="0">var result struct {
                Message string                      `json:"message"`
                Data    response.DyUserInfoResponse `json:"data"`
        }
        if err := json.Unmarshal(body, &amp;result); err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        <span class="cov0" title="0">if result.Data.OpenId == "" </span><span class="cov0" title="0">{
                return nil, errors.New("获取用户信息失败: " + result.Message)
        }</span>

        <span class="cov0" title="0">return &amp;result.Data, nil</span>
}

// AddUser 添加抖音授权用户
func (s *DyAuthUserService) AddUser(dyAuthUser *douyin.DyAuthUser) error <span class="cov0" title="0">{
        var existingUser douyin.DyAuthUser
        err := global.GVA_DB.Unscoped().Where("open_id = ?", dyAuthUser.OpenId).First(&amp;existingUser).Error
        if err == nil </span><span class="cov0" title="0">{
                // 用户已存在(包括已删除的)
                if existingUser.DeletedAt.Valid </span><span class="cov0" title="0">{
                        // 如果是已删除的记录，恢复并更新
                        return global.GVA_DB.Unscoped().Model(&amp;existingUser).Updates(map[string]interface{}{
                                "deleted_at":         nil,
                                "category_id":        dyAuthUser.CategoryId,
                                "sys_user_id":        dyAuthUser.SysUserId,
                                "access_token":       dyAuthUser.AccessToken,
                                "refresh_token":      dyAuthUser.RefreshToken,
                                "expires_in":         dyAuthUser.ExpiresIn,
                                "scope":              dyAuthUser.Scope,
                                "nickname":           dyAuthUser.Nickname,
                                "avatar":             dyAuthUser.Avatar,
                                "avatar_larger":      dyAuthUser.AvatarLarger,
                                "unique_id":          dyAuthUser.UniqueId,
                                "follower_count":     dyAuthUser.FollowerCount,
                                "following_count":    dyAuthUser.FollowingCount,
                                "aweme_count":        dyAuthUser.AwemeCount,
                                "total_favorited":    dyAuthUser.TotalFavorited,
                                "is_product_enabled": dyAuthUser.IsProductEnabled,
                        }).Error
                }</span>
                // 如果是未删除的记录，直接更新
                <span class="cov0" title="0">return global.GVA_DB.Model(&amp;existingUser).Updates(dyAuthUser).Error</span>
        }
        // 用户不存在,创建新用户
        <span class="cov0" title="0">return global.GVA_DB.Create(dyAuthUser).Error</span>
}

// GetUserList 获取抖音授权用户列表
func (s *DyAuthUserService) GetUserList(info request.DyAuthUserSearch, sysUserIds []uint) (list []douyin.DyAuthUser, total int64, err error) <span class="cov0" title="0">{
        limit := info.PageSize
        offset := info.PageSize * (info.Page - 1)
        db := global.GVA_DB.Model(&amp;douyin.DyAuthUser{})

        // 构建查询条件
        if info.CategoryId != nil &amp;&amp; *info.CategoryId != 0 </span><span class="cov0" title="0">{
                db = db.Where("category_id = ?", *info.CategoryId)
        }</span>
        <span class="cov0" title="0">if info.Nickname != nil &amp;&amp; *info.Nickname != "" </span><span class="cov0" title="0">{
                db = db.Where("nickname LIKE ?", "%"+*info.Nickname+"%")
        }</span>
        <span class="cov0" title="0">if info.UniqueId != nil &amp;&amp; *info.UniqueId != "" </span><span class="cov0" title="0">{
                db = db.Where("unique_id LIKE ?", "%"+*info.UniqueId+"%")
        }</span>
        // 添加sys_user_id查询条件，只能查询当前用户及其下级用户的抖音用户
        <span class="cov0" title="0">db = db.Where("sys_user_id IN ?", sysUserIds)

        // 获取总数
        err = db.Count(&amp;total).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, 0, err
        }</span>

        // 获取列表
        <span class="cov0" title="0">err = db.Limit(limit).Offset(offset).Order("created_at desc").Find(&amp;list).Error
        return list, total, err</span>
}

// DeleteUser 删除抖音授权用户
func (s *DyAuthUserService) DeleteUser(id uint) error <span class="cov0" title="0">{
        return global.GVA_DB.Delete(&amp;douyin.DyAuthUser{}, "id = ?", id).Error
}</span>

// GetUserByID 根据ID获取抖音授权用户
func (s *DyAuthUserService) GetUserByID(id uint) (*douyin.DyAuthUser, error) <span class="cov0" title="0">{
        var user douyin.DyAuthUser
        err := global.GVA_DB.Where("id = ?", id).First(&amp;user).Error
        return &amp;user, err
}</span>

// ToggleProductEnabled 切换选品状态
func (s *DyAuthUserService) ToggleProductEnabled(id uint, enabled bool) error <span class="cov0" title="0">{
        return global.GVA_DB.Model(&amp;douyin.DyAuthUser{}).Where("id = ?", id).Update("is_product_enabled", enabled).Error
}</span>

// RefreshToken 刷新访问令牌
func (s *DyAuthUserService) RefreshToken(refreshToken string) (*response.DyTokenResponse, error) <span class="cov0" title="0">{
        // 构建请求参数
        data := url.Values{}
        data.Set("client_key", ClientKey)
        data.Set("refresh_token", refreshToken)
        data.Set("grant_type", "refresh_token")

        // 发送请求
        resp, err := http.PostForm("https://open.douyin.com/oauth/refresh_token/", data)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>
        <span class="cov0" title="0">defer resp.Body.Close()

        // 读取响应
        body, err := ioutil.ReadAll(resp.Body)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        // 解析响应
        <span class="cov0" title="0">var result struct {
                Message string                   `json:"message"`
                Data    response.DyTokenResponse `json:"data"`
        }
        if err := json.Unmarshal(body, &amp;result); err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        <span class="cov0" title="0">if result.Data.AccessToken == "" </span><span class="cov0" title="0">{
                return nil, errors.New("刷新访问令牌失败: " + result.Message)
        }</span>

        <span class="cov0" title="0">return &amp;result.Data, nil</span>
}

// RefreshUserToken 刷新用户的访问令牌
func (s *DyAuthUserService) RefreshUserToken(id uint) error <span class="cov0" title="0">{
        // 获取用户信息
        user, err := s.GetUserByID(id)
        if err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        // 检查是否需要刷新令牌
        // 假设令牌有效期为15天，如果剩余时间不足3天，则刷新
        <span class="cov0" title="0">expiresAt := time.Now().Add(time.Duration(user.ExpiresIn) * time.Second)
        if time.Until(expiresAt) &gt; 3*24*time.Hour </span><span class="cov0" title="0">{
                return nil // 令牌还有足够的有效期，不需要刷新
        }</span>

        // 刷新令牌
        <span class="cov0" title="0">tokenInfo, err := s.RefreshToken(user.RefreshToken)
        if err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        // 更新用户的令牌信息
        <span class="cov0" title="0">return global.GVA_DB.Model(&amp;douyin.DyAuthUser{}).Where("id = ?", id).Updates(map[string]interface{}{
                "access_token":  tokenInfo.AccessToken,
                "refresh_token": tokenInfo.RefreshToken,
                "expires_in":    tokenInfo.ExpiresIn,
        }).Error</span>
}

// RefreshUserInfo 刷新用户信息
func (s *DyAuthUserService) RefreshUserInfo(id uint) error <span class="cov0" title="0">{
        // 获取用户信息
        user, err := s.GetUserByID(id)
        if err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        // 刷新用户的访问令牌（如果需要）
        <span class="cov0" title="0">if err := s.RefreshUserToken(id); err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        // 重新获取用户信息
        <span class="cov0" title="0">userInfo, err := s.GetUserInfoByAccessToken(user.AccessToken, user.OpenId)
        if err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        // 更新用户信息
        <span class="cov0" title="0">avatarBytes, _ := json.Marshal(userInfo.Avatar)
        avatarLargerBytes, _ := json.Marshal(userInfo.AvatarLarger)

        return global.GVA_DB.Model(&amp;douyin.DyAuthUser{}).Where("id = ?", id).Updates(map[string]interface{}{
                "nickname":        userInfo.Nickname,
                "avatar":          string(avatarBytes),
                "avatar_larger":   string(avatarLargerBytes),
                "unique_id":       userInfo.UniqueId,
                "follower_count":  userInfo.FollowerCount,
                "following_count": userInfo.FollowingCount,
                "aweme_count":     userInfo.AwemeCount,
                "total_favorited": userInfo.TotalFavorited,
        }).Error</span>
}
</pre>
		
		<pre class="file" id="file3" style="display: none">package douyin

import (
        "fmt"

        "github.com/flipped-aurora/gin-vue-admin/server/global"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
)

type DyAwemeService struct{}

var DyAwemeServiceApp = new(DyAwemeService)

// 通过awemeId获取dy_aweme视频信息
func (s *DyAwemeService) GetAwemeInfoByAwemeId(awemeId string) (resp douyin.DyAweme, err error) <span class="cov0" title="0">{
        err = global.GVA_DB.Where("aweme_id = ?", awemeId).First(&amp;resp).Error
        return resp, err
}</span>

// 创建作品
func (s *DyAwemeService) CreateAweme(aweme *douyin.DyAweme) (err error) <span class="cov0" title="0">{
        err = global.GVA_DB.Create(aweme).Error
        return err
}</span>

// 更新作品
func (s *DyAwemeService) UpdateAweme(aweme *douyin.DyAweme) (err error) <span class="cov0" title="0">{
        err = global.GVA_DB.Save(aweme).Error
        return err
}</span>

// 分页查询作品列表
func (s *DyAwemeService) GetAwemeList(pageInfo request.AwemeListSearch) (list []douyin.DyAweme, total int64, err error) <span class="cov0" title="0">{
        limit := pageInfo.PageSize
        offset := pageInfo.PageSize * (pageInfo.Page - 1)
        db := global.GVA_DB.Model(&amp;douyin.DyAweme{})
        if len(pageInfo.SysUserIds) &gt; 0 </span><span class="cov0" title="0">{
                db = db.Where("sys_user_id in ?", pageInfo.SysUserIds)
        }</span>

        <span class="cov0" title="0">fmt.Printf("pageInfo.UniqueIds: %v\n", pageInfo.UniqueIds)
        if len(pageInfo.UniqueIds) &gt; 0 </span><span class="cov0" title="0">{
                db = db.Where("unique_id in ? ", pageInfo.UniqueIds)
        }</span>

        <span class="cov0" title="0">var awemes []douyin.DyAweme
        err = db.Count(&amp;total).Error
        if err != nil </span><span class="cov0" title="0">{
                return awemes, total, err
        }</span>

        <span class="cov0" title="0">err = db.Limit(limit).Offset(offset).Order("create_time DESC").Find(&amp;awemes).Error
        return awemes, total, err</span>
}
</pre>
		
		<pre class="file" id="file4" style="display: none">package douyin

import (
        "github.com/flipped-aurora/gin-vue-admin/server/global"
        "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
)

type DyChatContactService struct{}

func (service *DyChatContactService) Create(contact *douyin.DyChatContact) error <span class="cov0" title="0">{
        return global.GVA_DB.Create(contact).Error
}</span>

func (service *DyChatContactService) Update(contact *douyin.DyChatContact) error <span class="cov0" title="0">{
        return global.GVA_DB.Model(&amp;douyin.DyChatContact{}).
                Where("id = ?", contact.ID).
                Updates(contact).Error
}</span>

func (service *DyChatContactService) GetList(pageInfo request.PageInfo) (list []douyin.DyChatContact, total int64, err error) <span class="cov0" title="0">{
        limit := pageInfo.PageSize
        offset := pageInfo.PageSize * (pageInfo.Page - 1)

        db := global.GVA_DB.Model(&amp;douyin.DyChatContact{})
        err = db.Count(&amp;total).Error
        if err != nil </span><span class="cov0" title="0">{
                return
        }</span>

        <span class="cov0" title="0">err = db.Limit(limit).Offset(offset).
                Order("last_msg_time DESC").
                Find(&amp;list).Error
        return</span>
}
</pre>
		
		<pre class="file" id="file5" style="display: none">package douyin

import (
        "github.com/flipped-aurora/gin-vue-admin/server/global"
        "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
)

type DyChatContactUserService struct{}

func (service *DyChatContactUserService) Create(contactUser *douyin.DyChatContactUser) error <span class="cov0" title="0">{
        return global.GVA_DB.Create(contactUser).Error
}</span>

func (service *DyChatContactUserService) Update(contactUser *douyin.DyChatContactUser) error <span class="cov0" title="0">{
        return global.GVA_DB.Model(&amp;douyin.DyChatContactUser{}).
                Where("id = ?", contactUser.ID).
                Updates(contactUser).Error
}</span>

func (service *DyChatContactUserService) GetList(pageInfo request.PageInfo) (list []douyin.DyChatContactUser, total int64, err error) <span class="cov0" title="0">{
        limit := pageInfo.PageSize
        offset := pageInfo.PageSize * (pageInfo.Page - 1)

        db := global.GVA_DB.Model(&amp;douyin.DyChatContactUser{})
        err = db.Count(&amp;total).Error
        if err != nil </span><span class="cov0" title="0">{
                return
        }</span>

        <span class="cov0" title="0">err = db.Limit(limit).Offset(offset).
                Order("created_at DESC").
                Find(&amp;list).Error
        return</span>
}
</pre>
		
		<pre class="file" id="file6" style="display: none">package douyin

import (
        "bytes"
        "encoding/json"
        "fmt"
        "io"
        "mime/multipart"
        "net/http"
        "net/url"
        "os"
        "path/filepath"
        "strconv"
        "time"

        "github.com/flipped-aurora/gin-vue-admin/server/global"
        "github.com/flipped-aurora/gin-vue-admin/server/utils"
        "github.com/valyala/fasthttp"
)

// DyHttpClient 抽象出来的HTTP客户端，用于与抖音API通信
type DyHttpClient struct {
        BaseURL string
        Client  *fasthttp.Client
}

// NewDyHttpClient 创建一个新的抖音HTTP客户端
func NewDyHttpClient(baseURL string) *DyHttpClient <span class="cov0" title="0">{
        return &amp;DyHttpClient{
                BaseURL: baseURL,
                Client: &amp;fasthttp.Client{
                        ReadTimeout:         60 * time.Second, // 增加读取超时时间
                        WriteTimeout:        30 * time.Second, // 增加写入超时时间
                        MaxIdleConnDuration: 30 * time.Second, // 增加空闲连接持续时间
                        MaxConnDuration:     60 * time.Second, // 增加连接最大持续时间
                        MaxConnsPerHost:     20,               // 增加每个主机的最大连接数
                },
        }
}</span>

// DoRequest 发送HTTP请求的通用方法
func (c *DyHttpClient) DoRequest(method, path string, reqBody map[string]any, respBody interface{}) error <span class="cov0" title="0">{
        if reqBody != nil &amp;&amp; reqBody["proxy"] == "" </span><span class="cov0" title="0">{
                reqBody["proxy"] = global.GVA_CONFIG.Kuaidaili.TunnelURL
        }</span>
        <span class="cov0" title="0">const maxRetries = 3
        var lastErr error

        for i := 0; i &lt; maxRetries; i++ </span><span class="cov0" title="0">{
                if i &gt; 0 </span><span class="cov0" title="0">{
                        time.Sleep(time.Duration(i*2) * time.Second) // 增加重试延迟
                }</span>

                <span class="cov0" title="0">jsonBody, err := json.Marshal(reqBody)
                if err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("marshal request body failed: %w", err)
                }</span>

                <span class="cov0" title="0">req := fasthttp.AcquireRequest()
                resp := fasthttp.AcquireResponse()
                defer fasthttp.ReleaseRequest(req)
                defer fasthttp.ReleaseResponse(resp)

                req.SetRequestURI(fmt.Sprintf("%s%s", c.BaseURL, path))
                req.Header.SetMethod(method)
                req.Header.SetContentType("application/json")

                // 修改连接头，使用 close 而不是 keep-alive
                req.Header.Set("Connection", "close")

                req.SetBody(jsonBody)

                // 记录请求开始时间
                requestStartTime := time.Now()
                global.GVA_LOG.Info(fmt.Sprintf("开始发送请求到 %s", path))

                // 设置更长的超时时间
                err = c.Client.DoTimeout(req, resp, 60*time.Second)

                // 记录请求结束时间和耗时
                requestDuration := time.Since(requestStartTime)
                global.GVA_LOG.Info(fmt.Sprintf("请求完成，耗时: %v", requestDuration))

                if err != nil </span><span class="cov0" title="0">{
                        lastErr = fmt.Errorf("request attempt %d failed: %w", i+1, err)
                        global.GVA_LOG.Error(fmt.Sprintf("请求失败，正在重试 (%d/%d): %v", i+1, maxRetries, err))
                        continue</span>
                }

                <span class="cov0" title="0">if resp.StatusCode() != fasthttp.StatusOK </span><span class="cov0" title="0">{
                        // 尝试解析响应体，看是否包含error字段
                        var errorResp struct {
                                Error string `json:"error"`
                        }

                        respBody := string(resp.Body())
                        decodedBody := respBody

                        // 尝试解析JSON
                        if err := json.Unmarshal(resp.Body(), &amp;errorResp); err == nil &amp;&amp; errorResp.Error != "" </span><span class="cov0" title="0">{
                                // 如果成功解析并且有error字段，进行URL解码
                                decodedError, _ := url.QueryUnescape(errorResp.Error)
                                decodedBody = fmt.Sprintf(`{"error": "%s"}`, decodedError)
                        }</span>

                        <span class="cov0" title="0">lastErr = fmt.Errorf("request failed with status code: %d, body: %s",
                                resp.StatusCode(), respBody)
                        global.GVA_LOG.Error(fmt.Sprintf("请求返回非200状态码，正在重试 (%d/%d): %v, 解码后: %s",
                                i+1, maxRetries, lastErr, decodedBody))
                        continue</span>
                }

                // 先尝试解析响应
                <span class="cov0" title="0">var rawResp struct {
                        Code int         `json:"code"`
                        Data interface{} `json:"data"`
                        Msg  string      `json:"msg"`
                }
                if err := json.Unmarshal(resp.Body(), &amp;rawResp); err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("unmarshal response body failed: %w, body: %s", err, string(resp.Body()))
                }</span>

                // 如果返回错误码,解码错误信息并返回
                <span class="cov0" title="0">if rawResp.Code != 0 </span><span class="cov0" title="0">{
                        decodedMsg, _ := url.QueryUnescape(rawResp.Msg)
                        return fmt.Errorf("api error: code=%d, msg=%s", rawResp.Code, decodedMsg)
                }</span>

                // 解析实际响应数据
                <span class="cov0" title="0">if err := json.Unmarshal(resp.Body(), respBody); err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("unmarshal response body failed: %w, body: %s", err, string(resp.Body()))
                }</span>

                <span class="cov0" title="0">global.GVA_LOG.Info(fmt.Sprintf("请求 %s 成功处理", path))
                return nil</span> // 请求成功，直接返回
        }

        <span class="cov0" title="0">return fmt.Errorf("all retry attempts failed, last error: %w", lastErr)</span>
}

// 发起表单请求
func (c *DyHttpClient) DoFormRequest(method, path string, formData map[string]string, respBody any) (err error) <span class="cov0" title="0">{
        if formData != nil &amp;&amp; formData["proxy"] == "" </span><span class="cov0" title="0">{
                formData["proxy"] = global.GVA_CONFIG.Kuaidaili.TunnelURL
        }</span>
        <span class="cov0" title="0">url := fmt.Sprintf("%s%s", c.BaseURL, path)
        req := fasthttp.AcquireRequest()
        defer fasthttp.ReleaseRequest(req)
        req.Header.SetMethod(method)
        req.SetRequestURI(url)
        // 创建multipart writer
        var body bytes.Buffer
        writer := multipart.NewWriter(&amp;body)
        for k, v := range formData </span><span class="cov0" title="0">{
                err = writer.WriteField(k, v)
                if err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("error writing field %s: %s -&gt; %v", k, v, err)
                }</span>
        }
        // 关闭writer以完成multipart构造
        <span class="cov0" title="0">err = writer.Close()
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("error closing writer: %v", err)
        }</span>

        <span class="cov0" title="0">req.Header.Set("Content-Type", writer.FormDataContentType())
        req.SetBody(body.Bytes())

        resp := fasthttp.AcquireResponse()
        defer fasthttp.ReleaseResponse(resp)

        // 使用 c.Client 并设置20秒超时
        timeoutSec := 20
        if _, ok := formData["timeoutSec"]; ok </span><span class="cov0" title="0">{
                timeoutSec, err = strconv.Atoi(formData["timeoutSec"])
        }</span>
        <span class="cov0" title="0">if err = c.Client.DoTimeout(req, resp, time.Duration(timeoutSec)*time.Second); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("url: %s 请求失败 %v", url, err)
        }</span>

        <span class="cov0" title="0">err = json.Unmarshal(resp.Body(), respBody)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("解析响应失败: %v", err)

        }</span>
        <span class="cov0" title="0">return nil</span>
}

// 发送HTTP请求(JSON格式)
func (c *DyHttpClient) DoJsonRequest(method, path string, reqBody map[string]any, respBody interface{}) error <span class="cov0" title="0">{
        if reqBody != nil &amp;&amp; reqBody["proxy"] == "" </span><span class="cov0" title="0">{
                reqBody["proxy"] = global.GVA_CONFIG.Kuaidaili.TunnelURL
        }</span>
        <span class="cov0" title="0">jsonBody, err := json.Marshal(reqBody)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("marshal request body failed: %w", err)
        }</span>
        <span class="cov0" title="0">req := fasthttp.AcquireRequest()
        resp := fasthttp.AcquireResponse()
        defer fasthttp.ReleaseRequest(req)
        defer fasthttp.ReleaseResponse(resp)

        req.SetRequestURI(fmt.Sprintf("%s%s", c.BaseURL, path))
        req.Header.SetMethod(method)
        req.Header.SetContentType("application/json")

        // 修改连接头，使用 close 而不是 keep-alive
        req.Header.Set("Connection", "close")

        req.SetBody(jsonBody)

        // 记录请求开始时间
        requestStartTime := time.Now()

        err = c.Client.DoTimeout(req, resp, 60*time.Second)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("url: %s 请求失败 %v", path, err)
        }</span>

        // 记录请求结束时间和耗时
        <span class="cov0" title="0">requestDuration := time.Since(requestStartTime)
        global.GVA_LOG.Info(fmt.Sprintf("请求完成，耗时: %v", requestDuration))
        // 解析实际响应数据
        if err := json.Unmarshal(resp.Body(), respBody); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("unmarshal response body failed: %w, body: %s", err, string(resp.Body()))
        }</span>
        <span class="cov0" title="0">return nil</span> // 请求成功，直接返回
}

// DoMultipartRequest 发送multipart/form-data请求，支持文件上传
func (c *DyHttpClient) DoMultipartRequest(
        method, path string,
        formData map[string]string,
        files map[string]string,
        remoteUrlMap map[string]string,
        respBody interface{},
) error <span class="cov0" title="0">{
        if formData != nil &amp;&amp; formData["proxy"] == "" </span><span class="cov0" title="0">{
                formData["proxy"] = global.GVA_CONFIG.Kuaidaili.TunnelURL
        }</span>
        <span class="cov0" title="0">url := fmt.Sprintf("%s%s", c.BaseURL, path)
        req := fasthttp.AcquireRequest()
        defer fasthttp.ReleaseRequest(req)

        req.Header.SetMethod(method)
        req.SetRequestURI(url)

        // 创建multipart writer
        body := &amp;bytes.Buffer{}
        writer := multipart.NewWriter(body)

        // 添加普通表单字段
        for k, v := range formData </span><span class="cov0" title="0">{
                if err := writer.WriteField(k, v); err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("写入表单字段失败 %s: %v", k, err)
                }</span>
        }

        <span class="cov0" title="0">if remoteUrlMap != nil </span><span class="cov0" title="0">{
                // 获取当前工作目录
                tempDir := filepath.Join(global.GVA_CONFIG.Local.Path, "post_temp")
                if err := utils.CreateDir(tempDir); err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("创建临时目录失败: %v", err)
                }</span>
                <span class="cov0" title="0">for k, remoteURL := range remoteUrlMap </span><span class="cov0" title="0">{
                        // 2. 生成文件名: 时间戳+随机字符串
                        timestamp := time.Now().Unix()
                        fileName := fmt.Sprintf("%d_%s", timestamp, filepath.Base(remoteURL))

                        // 3. 下载远程文件
                        filePath := filepath.Join(tempDir, fileName)
                        resp, err := http.Get(remoteURL)
                        if err != nil </span><span class="cov0" title="0">{
                                return fmt.Errorf("下载远程文件失败: %v", err)
                        }</span>
                        <span class="cov0" title="0">defer resp.Body.Close()

                        out, err := os.Create(filePath)
                        if err != nil </span><span class="cov0" title="0">{
                                return fmt.Errorf("创建本地文件失败: %v", err)
                        }</span>
                        <span class="cov0" title="0">defer out.Close()

                        _, err = io.Copy(out, resp.Body)
                        if err != nil </span><span class="cov0" title="0">{
                                return fmt.Errorf("保存文件内容失败: %v", err)
                        }</span>
                        <span class="cov0" title="0">defer os.Remove(filePath)

                        if files == nil </span><span class="cov0" title="0">{
                                files = make(map[string]string)
                        }</span>
                        <span class="cov0" title="0">files[k] = filePath</span>
                }
        }

        // 添加文件
        <span class="cov0" title="0">for fieldName, filePath := range files </span><span class="cov0" title="0">{
                file, err := os.Open(filePath)
                if err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("打开文件失败 %s: %v", filePath, err)
                }</span>
                <span class="cov0" title="0">defer file.Close()

                part, err := writer.CreateFormFile(fieldName, filepath.Base(filePath))
                if err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("创建文件表单失败: %v", err)
                }</span>

                <span class="cov0" title="0">if _, err := io.Copy(part, file); err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("复制文件内容失败: %v", err)
                }</span>
        }

        // 关闭writer
        <span class="cov0" title="0">if err := writer.Close(); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("关闭writer失败: %v", err)
        }</span>

        <span class="cov0" title="0">req.Header.SetContentType(writer.FormDataContentType())
        req.SetBody(body.Bytes())

        resp := fasthttp.AcquireResponse()
        defer fasthttp.ReleaseResponse(resp)

        if err := c.Client.DoTimeout(req, resp, 60*time.Second); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("请求失败: %v", err)
        }</span>

        <span class="cov0" title="0">if err := json.Unmarshal(resp.Body(), respBody); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("解析响应失败: %v", err)
        }</span>

        <span class="cov0" title="0">return nil</span>
}
</pre>
		
		<pre class="file" id="file7" style="display: none">package douyin

import (
        "encoding/json"
        "fmt"
        "io"
        "net/http"
        "os"
        "path/filepath"
        "regexp"
        "strconv"
        "strings"
        "time"

        "errors"

        "github.com/flipped-aurora/gin-vue-admin/server/global"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/response"
        "github.com/flipped-aurora/gin-vue-admin/server/model/system"
        "github.com/rs/xid"
        "gorm.io/gorm"
)

type DyProductService struct{}

// GrabDyProductList 获取抖音商品列表并存储
func (s *DyProductService) GrabDyProductList(
        req request.DyProductListRequest,
        dyUserID, sysUserID uint,
        collectType uint8,
) (err error) <span class="cov0" title="0">{
        var selectionID uint
        defer func() </span><span class="cov0" title="0">{
                if r := recover(); r != nil </span><span class="cov0" title="0">{
                        err = fmt.Errorf("捕获到异常: %v", r)
                }</span>
                <span class="cov0" title="0">if err != nil </span><span class="cov0" title="0">{
                        global.GVA_LOG.Error(fmt.Sprintf("获取抖音商品列表失败，错误: %v", err))
                        // 如果发生错误，更新选品记录状态为已中断
                        if selectionID &gt; 0 </span><span class="cov0" title="0">{
                                if updateErr := global.GVA_DB.Model(&amp;douyin.DyProductSelection{}).Where("id = ?", selectionID).
                                        Update("status", 3).Error; updateErr != nil </span><span class="cov0" title="0">{
                                        global.GVA_LOG.Error(fmt.Sprintf("更新选品记录状态失败: %v", updateErr))
                                }</span>
                        }
                }
        }()
        // 准备筛选条件
        <span class="cov0" title="0">filters := make(map[string]interface{})

        // 初始化游标和是否有更多数据的标志
        cursor := "0" // 初始游标值
        hasMore := true
        pageCount := 0 // 页码计数器

        // 保存符合条件的商品数量
        savedCount := 0

        // 用于控制请求频率的变量
        requestInterval := 5 * time.Second // 每5秒请求1次

        // 创建选品记录
        selection := &amp;douyin.DyProductSelection{
                DyUserID:    dyUserID,
                SysUserID:   sysUserID,
                Status:      1, // 默认进行中
                CollectType: collectType,
        }

        if err = global.GVA_DB.Create(selection).Error; err != nil </span><span class="cov0" title="0">{
                return err
        }</span>
        <span class="cov0" title="0">selectionID = selection.ID

        // 循环获取商品Feed流，直到没有更多数据
        hasStored := make(map[string]bool)
        for hasMore &amp;&amp; savedCount &lt; 2000 </span><span class="cov0" title="0">{
                // 记录本次请求开始时间
                requestStartTime := time.Now()
                global.GVA_LOG.Info(fmt.Sprintf("发送请求，游标: %s, 页码: %d", cursor, pageCount))

                // 调用抖音API获取商品Feed流
                productListReq := request.ProductListRequest{
                        Token:   req.Token,
                        Cursor:  cursor,
                        Filters: filters,
                }
                productListResp, err := AwemeApiServiceApp.GetProductList(productListReq)
                if err != nil </span><span class="cov0" title="0">{
                        if strings.Contains(err.Error(), "Expecting value: line 1 column 1 (char 0)") </span><span class="cov0" title="0">{
                                // 检查是否为空响应体错误
                                global.GVA_LOG.Error(fmt.Sprintf(
                                        "空响应体错误(Expecting value)，游标: %s, 页码: %d，等待 %v 后重试，错误: %v",
                                        cursor, pageCount, requestInterval, err,
                                ))
                                /* time.Sleep(requestInterval)
                                continue */
                        }</span> else<span class="cov0" title="0"> if strings.Contains(err.Error(), "unexpected end of JSON input") </span><span class="cov0" title="0">{
                                global.GVA_LOG.Error(fmt.Sprintf(
                                        "空响应体错误(unexpected end)，游标: %s, 页码: %d，等待 %v 后重试，错误: %v",
                                        cursor, pageCount, requestInterval, err,
                                ))
                                /* time.Sleep(requestInterval)
                                continue */
                        }</span>
                        <span class="cov0" title="0">return err</span>
                }

                // 处理当前页的商品
                <span class="cov0" title="0">var toGetProductDetail []request.GetAndSaveDyProductDetailRequest
                for _, promotion := range productListResp.Data.Promotions </span><span class="cov0" title="0">{
                        if _, ok := hasStored[promotion.ProductID]; ok </span><span class="cov0" title="0">{
                                global.GVA_LOG.Info(
                                        fmt.Sprintf("商品已存在，跳过，游标: %s, 页码: %d，商品ID: %s，等待 %v 后重试",
                                                cursor, pageCount, promotion.ProductID, requestInterval),
                                )
                                time.Sleep(requestInterval)
                                continue</span>
                        }
                        // 生成 record id
                        <span class="cov0" title="0">recordID := xid.New().String()
                        // 存储商品基本信息
                        images, _ := json.Marshal(promotion.BaseInfo.Images)
                        goodRatio, _ := strconv.ParseFloat(promotion.ManageInfo.GoodRatio, 64)
                        product := &amp;douyin.DyProductRecord{
                                SelectionID: selectionID,
                                RecordID:    recordID,
                                ProductID:   promotion.ProductID,
                                Title:       promotion.BaseInfo.Title,
                                Cover:       promotion.BaseInfo.Cover,
                                DetailURL:   promotion.BaseInfo.DetailURL,
                                Images:      string(images),
                                Price:       promotion.PriceInfo.Price,
                                Sales:       promotion.ManageInfo.Sales,
                                GoodRatio:   goodRatio,
                                CosFee:      promotion.CosInfo.CosFee,
                                CosRatio:    promotion.CosInfo.CosRatio,
                                DyUserID:    dyUserID,
                                SysUserID:   sysUserID,
                        }

                        // 创建商品记录
                        if err := global.GVA_DB.Create(product).Error; err != nil </span><span class="cov0" title="0">{
                                return err
                        }</span>
                        <span class="cov0" title="0">hasStored[promotion.ProductID] = true

                        savedCount++

                        // 更新选品记录的商品数量
                        if err := global.GVA_DB.Model(&amp;douyin.DyProductSelection{}).Where("id = ?", selectionID).
                                UpdateColumn("product_count", gorm.Expr("product_count + ?", 1)).Error; err != nil </span><span class="cov0" title="0">{
                                global.GVA_LOG.Error(fmt.Sprintf("更新选品记录商品数量失败: %v", err))
                        }</span>

                        <span class="cov0" title="0">toGetProductDetail = append(toGetProductDetail, request.GetAndSaveDyProductDetailRequest{
                                Token:     req.Token,
                                RecordID:  recordID,
                                ShopID:    promotion.ShopInfo.ShopID,
                                ShopName:  promotion.ShopInfo.ShopName,
                                ProductID: promotion.ProductID,
                        })</span>
                }

                // 检查是否有更多数据
                <span class="cov0" title="0">hasMore = productListResp.Data.HasMore

                // 如果没有更多数据，则退出循环
                if !hasMore </span><span class="cov0" title="0">{
                        global.GVA_LOG.Info(fmt.Sprintf("没有更多数据，退出循环，游标: %s, 页码: %d", cursor, pageCount))
                        break</span>
                }

                // 更新游标，继续获取下一页数据
                <span class="cov0" title="0">pageCount++
                if pageCount == 1 </span><span class="cov0" title="0">{
                        // 第二页的游标是8
                        cursor = "8"
                }</span> else<span class="cov0" title="0"> {
                        // 之后每页增加12
                        cursorInt, _ := strconv.Atoi(cursor)
                        cursor = strconv.Itoa(cursorInt + 12)
                }</span>

                // 计算本次请求的总耗时
                <span class="cov0" title="0">requestDuration := time.Since(requestStartTime)

                // 固定等待5秒，不考虑请求耗时
                global.GVA_LOG.Info(fmt.Sprintf("本次请求耗时: %v，等待 %v 后发送下一个请求", requestDuration, requestInterval))
                time.Sleep(requestInterval)

                // 获取商品详情
                for _, req := range toGetProductDetail </span><span class="cov0" title="0">{
                        err := s.SaveProductDetail(req)
                        if err != nil </span><span class="cov0" title="0">{
                                if strings.Contains(err.Error(), "Expecting value: line 1 column 1") </span><span class="cov0" title="0">{
                                        global.GVA_LOG.Error(fmt.Sprintf(
                                                "空响应体错误(Expecting value)，商品ID: %s，等待 %v 后重试，错误: %v",
                                                req.ProductID, requestInterval, err,
                                        ))
                                        time.Sleep(requestInterval)
                                        continue</span>
                                } else<span class="cov0" title="0"> if strings.Contains(err.Error(), "unexpected end of JSON input") </span><span class="cov0" title="0">{
                                        global.GVA_LOG.Error(fmt.Sprintf(
                                                "空响应体错误(unexpected end)，商品ID: %s，等待 %v 后重试，错误: %v",
                                                req.ProductID, requestInterval, err,
                                        ))
                                        time.Sleep(requestInterval)
                                        continue</span>
                                }
                                <span class="cov0" title="0">return err</span>
                        }
                }
        }

        // 如果没有保存任何商品，返回一个友好的错误
        <span class="cov0" title="0">if savedCount == 0 </span><span class="cov0" title="0">{
                // 更新选品记录状态为已中断
                if err := global.GVA_DB.Model(&amp;douyin.DyProductSelection{}).Where("id = ?", selectionID).
                        Update("status", 3).Error; err != nil </span><span class="cov0" title="0">{
                        global.GVA_LOG.Error(fmt.Sprintf("更新选品记录状态失败: %v", err))
                }</span>
                <span class="cov0" title="0">return errors.New("没有找到任何商品")</span>
        }

        // 更新选品记录状态为已完成
        <span class="cov0" title="0">if err := global.GVA_DB.Model(&amp;douyin.DyProductSelection{}).Where("id = ?", selectionID).
                Update("status", 2).Error; err != nil </span><span class="cov0" title="0">{
                global.GVA_LOG.Error(fmt.Sprintf("更新选品记录状态失败: %v", err))
        }</span>

        <span class="cov0" title="0">return nil</span>
}

// SaveProductDetail 保存商品详情和达人数据
func (s *DyProductService) SaveProductDetail(req request.GetAndSaveDyProductDetailRequest) error <span class="cov0" title="0">{
        // 用于控制请求频率
        requestInterval := 5 * time.Second // 每5秒请求1次

        // 1: 获取商品详细和推广数据并保存
        global.GVA_LOG.Info(fmt.Sprintf("开始获取商品详情，商品ID: %s", req.ProductID))
        requestStartTime := time.Now()

        packDetailReq := request.PackDetailRequest{
                Token:      req.Token,
                DataModule: "non-core",
                ProductID:  req.ProductID,
        }
        packDetailResp, err := AwemeApiServiceApp.GetPackDetail(packDetailReq)
        if err != nil </span><span class="cov0" title="0">{
                return err // 如果获取详情失败，返回错误
        }</span>

        // 计算本次请求的总耗时
        <span class="cov0" title="0">requestDuration := time.Since(requestStartTime)
        global.GVA_LOG.Info(fmt.Sprintf("获取商品详情请求耗时: %v", requestDuration))

        // 处理热门内容数据，提取前3的视频和前3的直播视频
        var hotContentData []map[string]interface{}
        videoCount := 0
        liveCount := 0

        // 遍历热门内容列表
        for _, content := range packDetailResp.Data.Model.HotContentData.ContentList </span><span class="cov0" title="0">{
                // 只保留前3个视频和前3个直播
                if content.ContentType == "video" &amp;&amp; videoCount &lt; 3 </span><span class="cov0" title="0">{
                        videoCount++
                        hotContentData = append(hotContentData, map[string]interface{}{
                                "content_type":        content.ContentType,
                                "content_id":          content.ContentID,
                                "author_name":         content.AuthorName,
                                "author_avatar":       content.AuthorAvatar,
                                "content_title":       content.ContentTitle,
                                "content_cover":       content.ContentCover,
                                "format_sales":        content.FormatSales,
                                "format_sales_amount": content.FormatSalesAmount,
                                "like_count":          content.LikeCount,
                                "play_count":          content.PlayCount,
                                "publish_time":        content.PublishTime,
                                "sales":               content.Sales,
                                "sales_amount":        content.SalesAmount,
                                "media_info": map[string]interface{}{
                                        "cover_url": content.MediaInfo.CoverURL,
                                        "duration":  content.MediaInfo.Duration,
                                        "item_id":   content.MediaInfo.ItemID,
                                        "video_url": content.MediaInfo.VideoURL,
                                },
                        })
                }</span> else<span class="cov0" title="0"> if content.ContentType == "live" &amp;&amp; liveCount &lt; 3 </span><span class="cov0" title="0">{
                        liveCount++
                        hotContentData = append(hotContentData, map[string]interface{}{
                                "content_type":        content.ContentType,
                                "content_id":          content.ContentID,
                                "author_name":         content.AuthorName,
                                "author_avatar":       content.AuthorAvatar,
                                "content_title":       content.ContentTitle,
                                "content_cover":       content.ContentCover,
                                "format_sales":        content.FormatSales,
                                "format_sales_amount": content.FormatSalesAmount,
                                "like_count":          content.LikeCount,
                                "play_count":          content.PlayCount,
                                "publish_time":        content.PublishTime,
                                "sales":               content.Sales,
                                "sales_amount":        content.SalesAmount,
                                "media_info": map[string]interface{}{
                                        "cover_url": content.MediaInfo.CoverURL,
                                        "duration":  content.MediaInfo.Duration,
                                        "item_id":   content.MediaInfo.ItemID,
                                        "video_url": content.MediaInfo.VideoURL,
                                },
                        })
                }</span>

                // 如果已经获取了足够的视频和直播，则退出循环
                <span class="cov0" title="0">if videoCount &gt;= 3 &amp;&amp; liveCount &gt;= 3 </span><span class="cov0" title="0">{
                        break</span>
                }
        }

        // 将热门内容数据转换为JSON字符串
        <span class="cov0" title="0">hotContentDataJSON, _ := json.Marshal(hotContentData)

        // 保存商品店铺数据
        calculateDataList, _ := json.Marshal(packDetailResp.Data.Model.ContentData.CalculateDataList)
        productData := &amp;douyin.DyProductShop{
                RecordID:                    req.RecordID,
                ProductID:                   packDetailResp.Data.ProductID,
                ShopID:                      req.ShopID,
                ShopName:                    req.ShopName,
                CalculateTime:               packDetailResp.Data.Model.ContentData.CalculateData.CalculateTime,
                BindShopMatchOrderNum:       packDetailResp.Data.Model.ContentData.CalculateData.BindShopMatchOrderNum,
                BindShopOrderConversionRate: packDetailResp.Data.Model.ContentData.CalculateData.BindShopOrderConversionRate,
                BindShopPv:                  packDetailResp.Data.Model.ContentData.CalculateData.BindShopPv,
                BindShopSales:               packDetailResp.Data.Model.ContentData.CalculateData.BindShopSales,
                BindShopSalesAmount:         packDetailResp.Data.Model.ContentData.CalculateData.BindShopSalesAmount,
                VideoCount:                  packDetailResp.Data.Model.ContentData.CalculateData.VideoCount,
                VideoMatchOrderNum:          packDetailResp.Data.Model.ContentData.CalculateData.VideoMatchOrderNum,
                VideoOrderConversionRate:    packDetailResp.Data.Model.ContentData.CalculateData.VideoOrderConversionRate,
                VideoPv:                     packDetailResp.Data.Model.ContentData.CalculateData.VideoPv,
                VideoSales:                  packDetailResp.Data.Model.ContentData.CalculateData.VideoSales,
                VideoSalesAmount:            packDetailResp.Data.Model.ContentData.CalculateData.VideoSalesAmount,
                VideoSalesContentNum:        packDetailResp.Data.Model.ContentData.CalculateData.VideoSalesContentNum,
                CalculateDataList:           string(calculateDataList),
                HotContentData:              string(hotContentDataJSON),
        }
        if err := global.GVA_DB.Create(productData).Error; err != nil </span><span class="cov0" title="0">{
                global.GVA_LOG.Error(fmt.Sprintf("保存商品店铺数据失败，商品ID: %s, 错误: %v", req.ProductID, err))
                return err
        }</span>

        <span class="cov0" title="0">global.GVA_LOG.Info(fmt.Sprintf("保存商品店铺数据完成，等待 %v 后开始获取带货达人榜单", requestInterval))
        time.Sleep(requestInterval)

        // 2: 获取带货达人榜单并保存
        global.GVA_LOG.Info(fmt.Sprintf("开始获取带货达人榜单，商品ID: %s", req.ProductID))
        requestStartTime = time.Now()

        authorListReq := request.AuthorListRequest{
                Token:      req.Token,
                DataModule: "dynamic",
                ProductID:  req.ProductID,
                DynamicParams: struct {
                        ParamType        int `json:"param_type"`
                        AuthorDataParams struct {
                                Count     int    `json:"count"`
                                TimeRange string `json:"time_range"`
                                Cursor    int    `json:"cursor"`
                        } `json:"author_data_params"`
                }{
                        ParamType: 2,
                        AuthorDataParams: struct {
                                Count     int    `json:"count"`
                                TimeRange string `json:"time_range"`
                                Cursor    int    `json:"cursor"`
                        }{
                                Count:     10,
                                TimeRange: "30",
                                Cursor:    0,
                        },
                },
        }
        authorListResp, err := AwemeApiServiceApp.GetAuthorList(authorListReq)
        if err != nil </span><span class="cov0" title="0">{
                return err // 如果获取达人榜单失败，返回错误
        }</span>

        // 计算本次请求的总耗时
        <span class="cov0" title="0">requestDuration = time.Since(requestStartTime)
        global.GVA_LOG.Info(fmt.Sprintf("获取带货达人榜单请求耗时: %v", requestDuration))

        // 保存带货达人榜单
        global.GVA_LOG.Info(fmt.Sprintf(
                "保存带货达人榜单，商品ID: %s, 达人数量: %d",
                req.ProductID,
                len(authorListResp.Data.Model.AuthorData.AuthorList),
        ))
        var rank int
        for _, author := range authorListResp.Data.Model.AuthorData.AuthorList </span><span class="cov0" title="0">{
                // 只获取关联视频的，不获取关联直播的
                if author.VideoCount &lt;= 0 </span><span class="cov0" title="0">{
                        continue</span>
                }
                <span class="cov0" title="0">rank++
                productAuthor := &amp;douyin.DyProductAuthor{
                        RecordID:       req.RecordID,
                        ProductID:      req.ProductID,
                        AuthorID:       author.AuthorID,
                        AuthorLevel:    author.AuthorLevel,
                        AuthorName:     author.AuthorName,
                        AvatarURL:      author.AvatarURL,
                        FormatSales:    author.FormatSales,
                        ImageTextCount: author.ImageTextCount,
                        LiveCount:      author.LiveCount,
                        Sales:          author.Sales,
                        ShopCount:      author.ShopCount,
                        VideoCount:     author.VideoCount,
                        Rank:           rank,
                }
                if err := global.GVA_DB.Create(productAuthor).Error; err != nil </span><span class="cov0" title="0">{
                        global.GVA_LOG.Error(fmt.Sprintf("保存带货达人数据失败，商品ID: %s, 达人ID: %s, 错误: %v", req.ProductID, author.AuthorID, err))
                        return err
                }</span>
        }
        <span class="cov0" title="0">global.GVA_LOG.Info(fmt.Sprintf("带货达人榜单保存成功，商品ID: %s, 达人数量: %d", req.ProductID, rank))

        // 在方法结束前添加等待时间，确保下一个请求不会立即发送
        global.GVA_LOG.Info(fmt.Sprintf("等待 %v 后处理下一个商品", requestInterval))
        time.Sleep(requestInterval)

        return nil</span>
}

// GetProductList 获取商品列表
func (s *DyProductService) GetProductList(
        req request.DyProductListDetailRequest,
        userID uint,
) (result response.DyProductListDetailResponse, err error) <span class="cov0" title="0">{
        db := global.GVA_DB.Model(&amp;douyin.DyProductRecord{})

        // 1. 获取商品列表和总数
        var products []response.DyProductInfo
        var total int64

        // 构建基础查询
        dyUserIds := strings.Split(req.DyUserIds, ",")
        dyUserIdsUint := make([]uint, 0, len(dyUserIds))
        for _, dyUserId := range dyUserIds </span><span class="cov0" title="0">{
                dyUserIdUint, err := strconv.ParseUint(dyUserId, 10, 64)
                if err != nil </span><span class="cov0" title="0">{
                        return response.DyProductListDetailResponse{}, err
                }</span>
                <span class="cov0" title="0">dyUserIdsUint = append(dyUserIdsUint, uint(dyUserIdUint))</span>
        }
        <span class="cov0" title="0">query := db.Where("dy_user_id IN (?)", dyUserIdsUint)

        // 添加筛选条件
        if req.Title != "" </span><span class="cov0" title="0">{
                query = query.Where("title LIKE ?", "%"+req.Title+"%")
        }</span>
        <span class="cov0" title="0">if req.MinPrice &gt; 0 </span><span class="cov0" title="0">{
                query = query.Where("price &gt;= ?", req.MinPrice*100)
        }</span>
        <span class="cov0" title="0">if req.MaxPrice &gt; 0 </span><span class="cov0" title="0">{
                query = query.Where("price &lt;= ?", req.MaxPrice*100)
        }</span>
        <span class="cov0" title="0">if req.MinSales &gt; 0 </span><span class="cov0" title="0">{
                query = query.Where("sales &gt;= ?", req.MinSales)
        }</span>
        <span class="cov0" title="0">if req.MinGoodRatio != "" </span><span class="cov0" title="0">{
                minGoodRatio, err := strconv.ParseFloat(req.MinGoodRatio, 64)
                if err == nil &amp;&amp; minGoodRatio &gt; 0 </span><span class="cov0" title="0">{
                        query = query.Where("good_ratio &gt;= ?", minGoodRatio)
                }</span>
        }
        <span class="cov0" title="0">if req.MinCosRatio &gt; 0 </span><span class="cov0" title="0">{
                query = query.Where("cos_ratio &gt;= ?", req.MinCosRatio)
        }</span>
        <span class="cov0" title="0">if req.MaxCosRatio &gt; 0 </span><span class="cov0" title="0">{
                query = query.Where("cos_ratio &lt;= ?", req.MaxCosRatio)
        }</span>

        // 添加选品时间范围筛选
        <span class="cov0" title="0">if req.StartTime != "" </span><span class="cov0" title="0">{
                query = query.Where("created_at &gt;= ?", req.StartTime+" 00:00:00")
        }</span>
        <span class="cov0" title="0">if req.EndTime != "" </span><span class="cov0" title="0">{
                query = query.Where("created_at &lt;= ?", req.EndTime+" 23:59:59")
        }</span>

        // 获取总数
        <span class="cov0" title="0">if err = query.Count(&amp;total).Error; err != nil </span><span class="cov0" title="0">{
                return
        }</span>

        // 如果有带货达人或直播销量筛选条件，需要重新计算total
        <span class="cov0" title="0">if req.MaxAuthorLevel &gt;= 0 || req.MinAuthorSales &gt; 0 || req.MinVideoSales &gt; 0 </span><span class="cov0" title="0">{
                // 创建一个临时查询，基于原始查询条件
                tempQuery := query

                // 使用子查询方式优化性能
                if req.MinVideoSales &gt; 0 </span><span class="cov0" title="0">{
                        // 查找符合视频销量条件的商品记录ID
                        shopSubQuery := global.GVA_DB.Model(&amp;douyin.DyProductShop{}).
                                Where("video_sales &gt;= ?", req.MinVideoSales).
                                Select("record_id")

                        tempQuery = tempQuery.Where("record_id IN (?)", shopSubQuery)
                }</span>

                <span class="cov0" title="0">if req.MaxAuthorLevel &gt;= 0 || req.MinAuthorSales &gt; 0 </span><span class="cov0" title="0">{
                        // 构建带货达人筛选子查询
                        authorSubQuery := global.GVA_DB.Model(&amp;douyin.DyProductAuthor{}).Select("DISTINCT record_id")

                        if req.MaxAuthorLevel &gt;= 0 </span><span class="cov0" title="0">{
                                authorSubQuery = authorSubQuery.Where("author_level &lt;= ?", req.MaxAuthorLevel)
                        }</span>

                        <span class="cov0" title="0">if req.MinAuthorSales &gt; 0 </span><span class="cov0" title="0">{
                                authorSubQuery = authorSubQuery.Where("sales &gt;= ?", req.MinAuthorSales)
                        }</span>

                        <span class="cov0" title="0">tempQuery = tempQuery.Where("record_id IN (?)", authorSubQuery)</span>
                }

                // 重新计算符合所有条件的商品总数
                <span class="cov0" title="0">if err = tempQuery.Count(&amp;total).Error; err != nil </span><span class="cov0" title="0">{
                        return
                }</span>
        }

        // 修改查询，确保字段名与结构体字段完全匹配
        <span class="cov0" title="0">if err = query.Scopes(req.Paginate()).
                Select(
                        "record_id, " +
                                "product_id, " +
                                "title, " +
                                "cover, " +
                                "detail_url, " +
                                "price, " +
                                "sales, " +
                                "good_ratio, " +
                                "cos_fee, " +
                                "cos_ratio, " +
                                "created_at",
                ).
                Order("sales desc").
                Scan(&amp;products).Error; err != nil </span><span class="cov0" title="0">{
                return
        }</span>

        // 准备返回结果列表
        <span class="cov0" title="0">productList := make([]response.DyProductListItem, 0, len(products))

        // 遍历每个商品，获取其对应的店铺信息和达人列表
        for _, product := range products </span><span class="cov0" title="0">{
                var item response.DyProductListItem
                item.Base = product

                // 2. 获取店铺信息
                var shop response.DyShopInfo
                var shopRecord douyin.DyProductShop
                err := global.GVA_DB.Model(&amp;douyin.DyProductShop{}).
                        Where("record_id = ?", product.RecordID).
                        Select(
                                "shop_name, " +
                                        "calculate_time, " +
                                        "bind_shop_order_conversion_rate, " +
                                        "bind_shop_pv, " +
                                        "bind_shop_sales, " +
                                        "bind_shop_sales_amount, " +
                                        "video_count, " +
                                        "video_match_order_num, " +
                                        "video_order_conversion_rate, " +
                                        "video_pv, " +
                                        "video_sales, " +
                                        "video_sales_amount, " +
                                        "calculate_data_list, " +
                                        "hot_content_data",
                        ).
                        First(&amp;shopRecord).Error

                // 如果获取店铺信息失败，使用零值
                if err == nil </span><span class="cov0" title="0">{
                        // 复制基本字段
                        shop.ShopName = shopRecord.ShopName
                        shop.CalculateTime = strconv.FormatInt(shopRecord.CalculateTime, 10)
                        shop.BindShopOrderConversionRate = shopRecord.BindShopOrderConversionRate
                        shop.BindShopPv = shopRecord.BindShopPv
                        shop.BindShopSales = shopRecord.BindShopSales
                        shop.BindShopSalesAmount = shopRecord.BindShopSalesAmount
                        shop.VideoCount = shopRecord.VideoCount
                        shop.VideoMatchOrderNum = shopRecord.VideoMatchOrderNum
                        shop.VideoOrderConversionRate = shopRecord.VideoOrderConversionRate
                        shop.VideoPv = shopRecord.VideoPv
                        shop.VideoSales = shopRecord.VideoSales
                        shop.VideoSalesAmount = shopRecord.VideoSalesAmount

                        // 解析统计数据列表
                        var calculateDataList []response.CalculateDataItem
                        if err := json.Unmarshal([]byte(shopRecord.CalculateDataList), &amp;calculateDataList); err == nil </span><span class="cov0" title="0">{
                                // 处理统计数据
                                for i := range calculateDataList </span><span class="cov0" title="0">{
                                        // 将字段名从下划线命名转换为驼峰命名
                                        item := response.CalculateDataItem{
                                                CalculateTime:            calculateDataList[i].CalculateTime,
                                                VideoCount:               calculateDataList[i].VideoCount,
                                                VideoMatchOrderNum:       calculateDataList[i].VideoMatchOrderNum,
                                                VideoOrderConversionRate: calculateDataList[i].VideoOrderConversionRate,
                                                VideoPv:                  calculateDataList[i].VideoPv,
                                                VideoSales:               calculateDataList[i].VideoSales,
                                                VideoSalesAmount:         calculateDataList[i].VideoSalesAmount,
                                        }
                                        shop.CalculateDataList = append(shop.CalculateDataList, item)
                                }</span>
                        }

                        // 解析热门内容数据
                        <span class="cov0" title="0">if shopRecord.HotContentData != "" </span><span class="cov0" title="0">{
                                var hotContentData []interface{}
                                if err := json.Unmarshal([]byte(shopRecord.HotContentData), &amp;hotContentData); err == nil </span><span class="cov0" title="0">{
                                        shop.HotContentData = hotContentData
                                }</span> else<span class="cov0" title="0"> {
                                        global.GVA_LOG.Warn(fmt.Sprintf("解析热门内容数据失败，商品ID: %s, 错误: %v", product.ProductID, err))
                                }</span>
                        }
                } else<span class="cov0" title="0"> {
                        // 记录日志但不返回错误
                        global.GVA_LOG.Warn(fmt.Sprintf("获取商品店铺数据失败，商品ID: %s, 错误: %v", product.ProductID, err))
                        // shop 结构体已经是零值，无需额外处理
                }</span>

                <span class="cov0" title="0">item.Shop = shop

                // 应用视频销量筛选条件
                if req.MinVideoSales &gt; 0 &amp;&amp; shop.VideoSales &lt; req.MinVideoSales </span><span class="cov0" title="0">{
                        continue</span>
                }

                // 3. 获取带货达人排行榜
                <span class="cov0" title="0">var authors []response.DyAuthorInfo
                authorQuery := global.GVA_DB.Model(&amp;douyin.DyProductAuthor{}).
                        Where("record_id = ?", product.RecordID)

                // 应用带货达人等级筛选条件
                if req.MaxAuthorLevel &gt;= 0 </span><span class="cov0" title="0">{
                        authorQuery = authorQuery.Where("author_level &lt;= ?", req.MaxAuthorLevel)
                }</span>

                // 应用带货达人销量筛选条件
                <span class="cov0" title="0">if req.MinAuthorSales &gt; 0 </span><span class="cov0" title="0">{
                        authorQuery = authorQuery.Where("sales &gt;= ?", req.MinAuthorSales)
                }</span>

                <span class="cov0" title="0">err = authorQuery.Select(
                        "author_id, " +
                                "author_level, " +
                                "author_name, " +
                                "avatar_url, " +
                                "format_sales, " +
                                "video_count, " +
                                "sales, " +
                                "`rank`",
                ).
                        Order("`rank` asc").
                        Limit(10).
                        Find(&amp;authors).Error

                // 如果获取达人列表失败或没有达人，使用空列表
                if err != nil || len(authors) == 0 </span><span class="cov0" title="0">{
                        global.GVA_LOG.Warn(fmt.Sprintf("获取商品达人列表失败，商品ID: %s, 错误: %v", product.ProductID, err))
                        authors = []response.DyAuthorInfo{} // 使用空列表
                }</span>

                // 如果应用了带货达人筛选条件但没有符合条件的达人，则跳过该商品
                <span class="cov0" title="0">if (req.MaxAuthorLevel &gt;= 0 || req.MinAuthorSales &gt; 0) &amp;&amp; len(authors) == 0 </span><span class="cov0" title="0">{
                        continue</span>
                }

                <span class="cov0" title="0">item.AuthorList = authors

                productList = append(productList, item)</span>
        }

        <span class="cov0" title="0">result = response.DyProductListDetailResponse{
                List:     productList,
                Total:    total,
                Page:     req.Page,
                PageSize: req.PageSize,
        }
        return</span>
}

// GetProductUserList 获取商品页抖音用户列表
func (s *DyProductService) GetProductUserList(userId uint) ([]response.ProductUserItem, error) <span class="cov0" title="0">{
        var result []response.ProductUserItem

        // 1. 获取当前用户信息
        var currentUser system.SysUser
        if err := global.GVA_DB.First(&amp;currentUser, userId).Error; err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        // 2. 获取当前用户及其下级用户的ID列表
        <span class="cov0" title="0">var subordinateUserIds []uint
        err := global.GVA_DB.Model(&amp;system.SysUser{}).Where("created_by = ?", userId).Pluck("id", &amp;subordinateUserIds).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("获取下级用户失败: %v", err)
        }</span>

        // 递归获取更深层级的下级用户
        <span class="cov0" title="0">for i := 0; i &lt; len(subordinateUserIds); i++ </span><span class="cov0" title="0">{
                var subUserIds []uint
                if err := global.GVA_DB.Model(&amp;system.SysUser{}).Where("created_by = ?", subordinateUserIds[i]).Pluck("id", &amp;subUserIds).Error; err != nil </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("获取下级用户失败: %v", err)
                }</span>
                <span class="cov0" title="0">subordinateUserIds = append(subordinateUserIds, subUserIds...)</span>
        }

        // 3. 将当前用户添加到结果中
        <span class="cov0" title="0">currentUserItem := response.ProductUserItem{
                SysNickname: currentUser.NickName,
                DyUsers:     []response.DyUser{},
        }

        // 4. 获取当前用户关联的抖音用户
        var currentDyUsers []douyin.DyUser
        if err := global.GVA_DB.Where("sys_user_id = ?", userId).Find(&amp;currentDyUsers).Error; err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        // 5. 将当前用户的抖音用户添加到结果中
        <span class="cov0" title="0">for _, dyUser := range currentDyUsers </span><span class="cov0" title="0">{
                currentUserItem.DyUsers = append(currentUserItem.DyUsers, response.DyUser{
                        ID:       dyUser.ID,
                        Nickname: dyUser.Nickname,
                })
        }</span>

        // 如果当前用户有抖音用户，添加到结果中
        <span class="cov0" title="0">if len(currentUserItem.DyUsers) &gt; 0 </span><span class="cov0" title="0">{
                result = append(result, currentUserItem)
        }</span>

        // 6. 处理下级用户
        <span class="cov0" title="0">if len(subordinateUserIds) &gt; 0 </span><span class="cov0" title="0">{
                var subUsers []system.SysUser
                if err := global.GVA_DB.Where("id IN (?)", subordinateUserIds).Find(&amp;subUsers).Error; err != nil </span><span class="cov0" title="0">{
                        return nil, err
                }</span>

                <span class="cov0" title="0">for _, subUser := range subUsers </span><span class="cov0" title="0">{
                        subUserItem := response.ProductUserItem{
                                SysNickname: subUser.NickName,
                                DyUsers:     []response.DyUser{},
                        }

                        // 7. 获取子用户关联的抖音用户
                        var subDyUsers []douyin.DyUser
                        if err := global.GVA_DB.Where("sys_user_id = ?", subUser.ID).Find(&amp;subDyUsers).Error; err != nil </span><span class="cov0" title="0">{
                                return nil, err
                        }</span>

                        // 8. 将子用户的抖音用户添加到结果中
                        <span class="cov0" title="0">for _, dyUser := range subDyUsers </span><span class="cov0" title="0">{
                                subUserItem.DyUsers = append(subUserItem.DyUsers, response.DyUser{
                                        ID:       dyUser.ID,
                                        Nickname: dyUser.Nickname,
                                })
                        }</span>

                        // 如果子用户有抖音用户，添加到结果中
                        <span class="cov0" title="0">if len(subUserItem.DyUsers) &gt; 0 </span><span class="cov0" title="0">{
                                result = append(result, subUserItem)
                        }</span>
                }
        }

        <span class="cov0" title="0">return result, nil</span>
}

// DownloadDouyinVideo 下载抖音无水印视频
func DownloadDouyinVideo(url string) error <span class="cov0" title="0">{
        // 使用正则提取 video_id
        re := regexp.MustCompile(`/video/(\d+)`)
        matches := re.FindStringSubmatch(url)
        if len(matches) &lt; 2 </span><span class="cov0" title="0">{
                return fmt.Errorf("无法解析视频 ID")
        }</span>
        <span class="cov0" title="0">videoID := matches[1]

        // 构造无水印视频 URL
        noWatermarkURL := fmt.Sprintf("https://aweme.snssdk.com/aweme/v1/play/?video_id=%s&amp;ratio=720p&amp;line=0", videoID)

        // 发送请求获取视频数据
        resp, err := httpGet(noWatermarkURL)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("请求视频失败: %v", err)
        }</span>
        <span class="cov0" title="0">defer resp.Body.Close()

        // 检查响应状态
        if resp.StatusCode != http.StatusOK </span><span class="cov0" title="0">{
                return fmt.Errorf("下载失败, HTTP 状态码: %d", resp.StatusCode)
        }</span>

        // 获取当前工作目录
        <span class="cov0" title="0">currentDir, err := os.Getwd()
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("获取当前工作目录失败: %v", err)
        }</span>

        // 创建文件保存视频，使用完整路径
        <span class="cov0" title="0">fileName := fmt.Sprintf("%s.mp4", videoID)
        filePath := filepath.Join(currentDir, fileName)
        file, err := os.Create(filePath)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("创建文件失败: %v", err)
        }</span>
        <span class="cov0" title="0">defer file.Close()

        // 将视频数据写入文件
        _, err = io.Copy(file, resp.Body)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("写入文件失败: %v", err)
        }</span>

        <span class="cov0" title="0">fmt.Printf("视频下载成功: %s\n", filePath)
        return nil</span>
}

// 声明一个变量来存储http.Get函数，以便在测试中替换它
var httpGet = http.Get
</pre>
		
		<pre class="file" id="file8" style="display: none">package douyin

import (
        "errors"
        "time"

        "github.com/flipped-aurora/gin-vue-admin/server/global"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
        dyRequest "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
        "gorm.io/gorm"
)

// CreateProductManual 创建手动录入商品
func (s *DyProductService) CreateProductManual(req dyRequest.DyProductManualRequest, userId uint) error <span class="cov0" title="0">{
        // 计算佣金金额
        cosFee := int64(float64(req.Price) * req.CosRatio / 100)

        product := douyin.DyProductManual{
                Title:          req.Title,
                Cover:          req.Cover,
                Price:          req.Price,
                PromotionTitle: req.PromotionTitle,
                PromotionUrl:   req.PromotionUrl,
                CosRatio:       req.CosRatio,
                CosFee:         cosFee,
                DyUserId:       req.DyUserId,
                UserId:         userId,
        }

        return global.GVA_DB.Create(&amp;product).Error
}</span>

// UpdateProductManual 更新手动录入商品
func (s *DyProductService) UpdateProductManual(id uint, req dyRequest.DyProductManualRequest) error <span class="cov0" title="0">{
        // 计算佣金金额
        cosFee := int64(float64(req.Price) * req.CosRatio / 100)

        updates := map[string]interface{}{
                "title":           req.Title,
                "cover":           req.Cover,
                "price":           req.Price,
                "promotion_title": req.PromotionTitle,
                "promotion_url":   req.PromotionUrl,
                "cos_ratio":       req.CosRatio,
                "cos_fee":         cosFee,
                "dy_user_id":      req.DyUserId,
        }

        return global.GVA_DB.Model(&amp;douyin.DyProductManual{}).Where("id = ?", id).Updates(updates).Error
}</span>

// GetProductManualList 获取手动录入商品列表
func (s *DyProductService) GetProductManualList(req dyRequest.DyProductManualListRequest, userId uint) (list []douyin.DyProductManual, total int64, err error) <span class="cov0" title="0">{
        limit := req.PageSize
        offset := req.PageSize * (req.Page - 1)
        db := global.GVA_DB.Model(&amp;douyin.DyProductManual{})

        // 构建查询条件
        if req.Title != "" </span><span class="cov0" title="0">{
                db = db.Where("title LIKE ?", "%"+req.Title+"%")
        }</span>
        <span class="cov0" title="0">if req.DyUserId != 0 </span><span class="cov0" title="0">{
                db = db.Where("dy_user_id = ?", req.DyUserId)
        }</span>
        <span class="cov0" title="0">if req.StartTime != "" &amp;&amp; req.EndTime != "" </span><span class="cov0" title="0">{
                startTime, _ := time.Parse("2006-01-02", req.StartTime)
                endTime, _ := time.Parse("2006-01-02", req.EndTime)
                endTime = endTime.Add(24 * time.Hour)
                db = db.Where("created_at &gt;= ? AND created_at &lt; ?", startTime, endTime)
        }</span>

        // 获取总数
        <span class="cov0" title="0">err = db.Count(&amp;total).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, 0, err
        }</span>

        // 获取列表
        <span class="cov0" title="0">err = db.Limit(limit).Offset(offset).Order("created_at DESC").Find(&amp;list).Error
        return list, total, err</span>
}

// GetProductManualById 根据ID获取手动录入商品
func (s *DyProductService) GetProductManualById(id uint) (product douyin.DyProductManual, err error) <span class="cov0" title="0">{
        err = global.GVA_DB.Where("id = ?", id).First(&amp;product).Error
        if err != nil </span><span class="cov0" title="0">{
                if errors.Is(err, gorm.ErrRecordNotFound) </span><span class="cov0" title="0">{
                        return product, errors.New("商品不存在")
                }</span>
                <span class="cov0" title="0">return product, err</span>
        }
        <span class="cov0" title="0">return product, nil</span>
}

// DeleteProductManual 删除手动录入商品
func (s *DyProductService) DeleteProductManual(id uint) error <span class="cov0" title="0">{
        return global.GVA_DB.Delete(&amp;douyin.DyProductManual{}, id).Error
}</span>
</pre>
		
		<pre class="file" id="file9" style="display: none">package douyin

import (
        "encoding/json"
        "time"

        "github.com/flipped-aurora/gin-vue-admin/server/global"
        "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
        douyinReq "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
        "gorm.io/gorm"
)

type DyProductSelectionService struct{}

// GetProductSelectionList 获取商品选品记录列表
func (s *DyProductSelectionService) GetProductSelectionList(
        info douyinReq.ProductSelectionListRequest,
) (list []map[string]interface{}, total int64, err error) <span class="cov0" title="0">{
        // 先更新超过5分钟未更新且状态为"进行中"的记录状态为"已中断"
        updateSQL := `
                UPDATE dy_product_selection 
                SET status = 3 
                WHERE status = 1 
                AND updated_at &lt; DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        `
        global.GVA_DB.Exec(updateSQL)

        limit := info.PageSize
        offset := info.PageSize * (info.Page - 1)

        // 定义结果结构体
        type ProductSelectionWithNames struct {
                ID           uint           `json:"id" gorm:"column:id"`
                CreatedAt    time.Time      `json:"createdAt" gorm:"column:createdAt"`
                UpdatedAt    time.Time      `json:"updatedAt" gorm:"column:updatedAt"`
                DeletedAt    gorm.DeletedAt `json:"deletedAt" gorm:"column:deletedAt"`
                DyUserId     uint           `json:"dyUserId" gorm:"column:dyUserId"`
                SysUserId    uint           `json:"sysUserId" gorm:"column:sysUserId"`
                ProductCount uint           `json:"productCount" gorm:"column:productCount"`
                Status       uint8          `json:"status" gorm:"column:status"`
                CollectType  uint8          `json:"collectType" gorm:"column:collectType"`
                DyUserName   string         `json:"dyUserName" gorm:"column:dy_user_name"`
                SysUserName  string         `json:"sysUserName" gorm:"column:sys_user_name"`
        }

        // 构建基础查询
        db := global.GVA_DB.Model(&amp;douyin.DyProductSelection{})

        // 应用过滤条件
        if info.Status != 0 </span><span class="cov0" title="0">{
                db = db.Where("dy_product_selection.status = ?", info.Status)
        }</span>

        // 计算总数 - 这里不包含DyUserName筛选，因为它需要联表查询
        <span class="cov0" title="0">var count int64
        err = db.Count(&amp;count).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, 0, err
        }</span>

        // 构建联表查询SQL
        <span class="cov0" title="0">query := `
                SELECT 
                        dps.id,
                        dps.created_at AS createdAt,
                        dps.updated_at AS updatedAt,
                        dps.deleted_at AS deletedAt,
                        dps.dy_user_id AS dyUserId,
                        dps.sys_user_id AS sysUserId,
                        dps.product_count AS productCount,
                        dps.status,
                        dps.collect_type AS collectType,
                        du.nickname AS dy_user_name,
                        su.nick_name AS sys_user_name
                FROM 
                        dy_product_selection dps
                LEFT JOIN 
                        dy_user du ON dps.dy_user_id = du.id
                LEFT JOIN 
                        sys_users su ON dps.sys_user_id = su.id
                WHERE 1=1
        `

        // 添加过滤条件
        args := []interface{}{}
        if info.Status != 0 </span><span class="cov0" title="0">{
                query += " AND dps.status = ?"
                args = append(args, info.Status)
        }</span>
        <span class="cov0" title="0">if info.DyUserName != "" </span><span class="cov0" title="0">{
                query += " AND du.nickname LIKE ?"
                args = append(args, "%"+info.DyUserName+"%")
        }</span>

        // 添加排序和分页
        <span class="cov0" title="0">query += " ORDER BY dps.id DESC LIMIT ? OFFSET ?"
        args = append(args, limit, offset)

        // 执行查询
        var result []ProductSelectionWithNames
        err = global.GVA_DB.Raw(query, args...).Scan(&amp;result).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, 0, err
        }</span>

        // 如果使用了抖音用户名筛选，需要重新计算总数
        <span class="cov0" title="0">if info.DyUserName != "" </span><span class="cov0" title="0">{
                countQuery := `
                        SELECT COUNT(*) 
                        FROM dy_product_selection dps
                        LEFT JOIN dy_user du ON dps.dy_user_id = du.id
                        WHERE du.nickname LIKE ?
                `
                countArgs := []interface{}{"%" + info.DyUserName + "%"}

                if info.Status != 0 </span><span class="cov0" title="0">{
                        countQuery += " AND dps.status = ?"
                        countArgs = append(countArgs, info.Status)
                }</span>

                <span class="cov0" title="0">global.GVA_DB.Raw(countQuery, countArgs...).Scan(&amp;count)</span>
        }

        // 将结构体结果转换为map
        <span class="cov0" title="0">var mapResult []map[string]interface{}
        jsonData, _ := json.Marshal(result)
        json.Unmarshal(jsonData, &amp;mapResult)

        return mapResult, count, nil</span>
}

// UpdateProductSelection 更新商品选品记录
func (s *DyProductSelectionService) UpdateProductSelection(productSelection douyin.DyProductSelection) (err error) <span class="cov0" title="0">{
        return global.GVA_DB.Updates(&amp;productSelection).Error
}</span>

// DeleteProductSelection 删除商品选品记录
func (s *DyProductSelectionService) DeleteProductSelection(ids request.IdsReq) (err error) <span class="cov0" title="0">{
        return global.GVA_DB.Delete(&amp;[]douyin.DyProductSelection{}, "id in ?", ids.Ids).Error
}</span>

// UpdateProductCount 更新商品数量
func (s *DyProductSelectionService) UpdateProductCount(id uint, count uint) (err error) <span class="cov0" title="0">{
        return global.GVA_DB.Model(&amp;douyin.DyProductSelection{}).Where("id = ?", id).Update("product_count", count).Error
}</span>

// UpdateStatus 更新状态
func (s *DyProductSelectionService) UpdateStatus(id uint, status uint8) (err error) <span class="cov0" title="0">{
        return global.GVA_DB.Model(&amp;douyin.DyProductSelection{}).Where("id = ?", id).Update("status", status).Error
}</span>

// IncrementProductCount 增加商品数量
func (s *DyProductSelectionService) IncrementProductCount(id uint, increment uint) (err error) <span class="cov0" title="0">{
        return global.GVA_DB.Model(&amp;douyin.DyProductSelection{}).Where("id = ?", id).
                UpdateColumn("product_count", gorm.Expr("product_count + ?", increment)).Error
}</span>
</pre>
		
		<pre class="file" id="file10" style="display: none">package douyin

import (
        "strconv"
        "strings"

        "github.com/flipped-aurora/gin-vue-admin/server/global"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
)

type DyTopCommentService struct{}

func (d *DyTopCommentService) GetCommentList(info request.DyTopCommentListSearch, sysUserIds []uint) (list []douyin.DyTopComment, total int64, err error) <span class="cov0" title="0">{
        // 设置分页大小
        limit := info.PageSize
        // 计算偏移量
        offset := info.PageSize * (info.Page - 1)

        // 创建查询构造器
        db := global.GVA_DB.Model(&amp;douyin.DyTopComment{})

        // 构建查询条件
        if info.AwemeId != "" </span><span class="cov0" title="0">{
                // 如果指定了短视频ID，则添加查询条件
                db = db.Where("aweme_id = ?", info.AwemeId)
        }</span>

        <span class="cov0" title="0">if info.DyUserIds != "" </span><span class="cov0" title="0">{
                dyUserIds := strings.Split(info.DyUserIds, ",")
                dyUserIdsUint := make([]uint, 0, len(dyUserIds))
                for _, dyUserId := range dyUserIds </span><span class="cov0" title="0">{
                        dyUserIdUint, err := strconv.ParseUint(dyUserId, 10, 64)
                        if err != nil </span><span class="cov0" title="0">{
                                return list, 0, err
                        }</span>
                        <span class="cov0" title="0">dyUserIdsUint = append(dyUserIdsUint, uint(dyUserIdUint))</span>
                }
                <span class="cov0" title="0">if len(dyUserIdsUint) &gt; 0 </span><span class="cov0" title="0">{
                        db = db.Where("dy_user_id in ?", dyUserIdsUint)
                }</span>
        }

        <span class="cov0" title="0">if len(sysUserIds) &gt; 0 </span><span class="cov0" title="0">{
                // 如果指定了系统用户ID列表，则添加查询条件
                db = db.Where("sys_user_id in ?", sysUserIds)
        }</span>

        // 执行查询
        // 执行查询，获取总数
        <span class="cov0" title="0">err = db.Count(&amp;total).Error
        // 如果查询出错或总数为0，则直接返回
        if err != nil || total == 0 </span><span class="cov0" title="0">{
                return
        }</span>

        // 执行查询，获取分页结果
        <span class="cov0" title="0">err = db.Limit(limit).Offset(offset).Order("created_at desc").Find(&amp;list).Error
        return</span>
}
</pre>
		
		<pre class="file" id="file11" style="display: none">package douyin

import (
        "context"
        "encoding/json"
        "fmt"
        "time"

        "github.com/flipped-aurora/gin-vue-admin/server/global"
        "github.com/flipped-aurora/gin-vue-admin/server/model/creative"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
        "go.uber.org/zap"
        "gorm.io/gorm"
)

type DyUserService struct{}

var DyUserServiceApp = new(DyUserService)

func (s *DyUserService) AddUser(dyUser *douyin.DyUser) error <span class="cov0" title="0">{
        var existingUser douyin.DyUser
        err := global.GVA_DB.Unscoped().Where("uid = ?", dyUser.UID).First(&amp;existingUser).Error
        if err == nil </span><span class="cov0" title="0">{
                // 用户已存在(包括已删除的)
                if existingUser.DeletedAt.Valid </span><span class="cov0" title="0">{
                        // 如果是已删除的记录，恢复并更新
                        return global.GVA_DB.Unscoped().Model(&amp;existingUser).Updates(map[string]interface{}{
                                "deleted_at":         nil,
                                "im_token":           dyUser.ImToken,
                                "category_id":        dyUser.CategoryId,
                                "nickname":           dyUser.Nickname,
                                "avatar":             dyUser.Avatar,
                                "unique_id":          dyUser.UniqueId,
                                "short_id":           dyUser.ShortId,
                                "sec_uid":            dyUser.SecUid,
                                "follower_count":     dyUser.FollowerCount,
                                "total_favorited":    dyUser.TotalFavorited,
                                "account_region":     dyUser.AccountRegion,
                                "province":           dyUser.Province,
                                "city":               dyUser.City,
                                "college_name":       dyUser.CollegeName,
                                "bind_phone":         dyUser.BindPhone,
                                "birthday":           dyUser.Birthday,
                                "gender":             dyUser.Gender,
                                "signature":          dyUser.Signature,
                                "sys_user_id":        dyUser.SysUserId,
                                "is_product_enabled": dyUser.IsProductEnabled,
                        }).Error
                }</span>
                // 如果是未删除的记录，直接更新
                <span class="cov0" title="0">return global.GVA_DB.Model(&amp;existingUser).Updates(dyUser).Error</span>
        }
        // 用户不存在,创建新用户
        <span class="cov0" title="0">err = global.GVA_DB.Create(dyUser).Error
        if err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        // 获取当前用户的排序列表
        <span class="cov0" title="0">if dyUser.SysUserId &gt; 0 </span><span class="cov0" title="0">{
                sysUserId := uint(dyUser.SysUserId)
                // 获取全部分类的排序列表
                allCategoryUserIds, err := s.GetUserSort(sysUserId, nil)
                if err == nil </span><span class="cov0" title="0">{
                        // 将新用户添加到列表首位
                        newUserIds := append([]uint{dyUser.ID}, allCategoryUserIds...)
                        // 保存更新后的排序
                        err = s.SaveUserSort(sysUserId, nil, newUserIds)
                        if err != nil </span><span class="cov0" title="0">{
                                global.GVA_LOG.Error("保存全部分类排序失败", zap.Error(err))
                        }</span>
                }

                // 如果用户有分类，获取该分类的排序列表
                <span class="cov0" title="0">if dyUser.CategoryId &gt; 0 </span><span class="cov0" title="0">{
                        categoryId := uint(dyUser.CategoryId)
                        categoryUserIds, err := s.GetUserSort(sysUserId, &amp;categoryId)
                        if err == nil </span><span class="cov0" title="0">{
                                // 将新用户添加到列表首位
                                newUserIds := append([]uint{dyUser.ID}, categoryUserIds...)
                                // 保存更新后的排序
                                err = s.SaveUserSort(sysUserId, &amp;categoryId, newUserIds)
                                if err != nil </span><span class="cov0" title="0">{
                                        global.GVA_LOG.Error("保存分类排序失败", zap.Error(err))
                                }</span>
                        }
                }
        }

        <span class="cov0" title="0">return nil</span>
}

// AddUserWithDevice 添加用户并绑定设备
func (s *DyUserService) AddUserWithDevice(dyUser *douyin.DyUser) error <span class="cov0" title="0">{
        return global.GVA_DB.Create(dyUser).Error
}</span>

// GetUserList 获取抖音用户列表
func (s *DyUserService) GetUserList(info request.DyUserSearch, sysUserIds []uint) (list []douyin.DyUser, total int64, err error) <span class="cov0" title="0">{
        limit := info.PageSize
        offset := info.PageSize * (info.Page - 1)
        db := global.GVA_DB.Model(&amp;douyin.DyUser{})

        // 构建查询条件
        if info.InvalidLogin == 1 </span><span class="cov0" title="0">{
                // 查询无效登录的用户
                db = db.Where("status = ?", 0)
        }</span>

        <span class="cov0" title="0">if info.CategoryId != nil &amp;&amp; *info.CategoryId != 0 </span><span class="cov0" title="0">{
                db = db.Where("category_id = ?", *info.CategoryId)
        }</span>
        <span class="cov0" title="0">if info.Nickname != nil &amp;&amp; *info.Nickname != "" </span><span class="cov0" title="0">{
                db = db.Where("nickname LIKE ?", "%"+*info.Nickname+"%")
        }</span>
        <span class="cov0" title="0">if info.UniqueId != nil &amp;&amp; *info.UniqueId != "" </span><span class="cov0" title="0">{
                db = db.Where("unique_id LIKE ?", "%"+*info.UniqueId+"%")
        }</span>
        // 添加sys_user_id查询条件，只能查询当前用户及其下级用户的抖音用户
        <span class="cov0" title="0">db = db.Where("sys_user_id IN ?", sysUserIds)

        // 获取总数
        err = db.Count(&amp;total).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, 0, err
        }</span>

        // 获取列表，关联 sys_users 表以获取录入者信息
        <span class="cov0" title="0">var users []struct {
                douyin.DyUser
                SysUserName   string `json:"sysUserName"`   // 录入者名称
                PhoneUserName string `json:"phoneUserName"` // 手机号实名
                PhoneOperator string `json:"phoneOperator"` // 手机号运营商
        }

        // 尝试获取用户自定义排序
        var sortedUserIds []uint
        if info.CurrentUserID &gt; 0 </span><span class="cov0" title="0">{
                sortedUserIds, _ = s.GetUserSort(info.CurrentUserID, info.CategoryId)
        }</span>

        // 根据是否有自定义排序决定查询方式
        <span class="cov0" title="0">if len(sortedUserIds) &gt; 0 </span><span class="cov0" title="0">{
                // 有自定义排序，先获取所有数据然后排序
                err = db.Select(
                        "dy_user.*, sys_users.nick_name as sys_user_name, phone_balance.real_name as phone_user_name, phone_balance.operator_type as phone_operator").
                        Joins("left join sys_users on dy_user.sys_user_id = sys_users.id").
                        Joins("left join phone_balance on dy_user.bind_phone = phone_balance.phone_number AND phone_balance.deleted_at IS NULL").
                        Order("dy_user.created_at desc").
                        Find(&amp;users).Error

                if err != nil </span><span class="cov0" title="0">{
                        return nil, 0, err
                }</span>

                // 创建ID到用户的映射
                <span class="cov0" title="0">userMap := make(map[uint]struct {
                        douyin.DyUser
                        SysUserName   string `json:"sysUserName"`
                        PhoneUserName string `json:"phoneUserName"`
                        PhoneOperator string `json:"phoneOperator"`
                })
                for _, user := range users </span><span class="cov0" title="0">{
                        userMap[user.ID] = user
                }</span>

                // 按自定义顺序重新排列
                <span class="cov0" title="0">var sortedUsers []struct {
                        douyin.DyUser
                        SysUserName   string `json:"sysUserName"`
                        PhoneUserName string `json:"phoneUserName"`
                        PhoneOperator string `json:"phoneOperator"`
                }

                // 先添加已排序的用户
                for _, id := range sortedUserIds </span><span class="cov0" title="0">{
                        if user, exists := userMap[id]; exists </span><span class="cov0" title="0">{
                                sortedUsers = append(sortedUsers, user)
                                delete(userMap, id) // 删除已添加的用户
                        }</span>
                }

                // 再添加未排序的用户（按创建时间倒序）
                // 将已排序的用户ID转换为map，提高查找效率
                <span class="cov0" title="0">sortedUserMap := make(map[uint]bool, len(sortedUserIds))
                for _, id := range sortedUserIds </span><span class="cov0" title="0">{
                        sortedUserMap[id] = true
                }</span>

                // 直接从已排序的users切片中筛选剩余用户
                <span class="cov0" title="0">for _, user := range users </span><span class="cov0" title="0">{
                        // 如果用户ID不在已排序列表中，则添加到剩余用户列表
                        if !sortedUserMap[user.ID] </span><span class="cov0" title="0">{
                                sortedUsers = append(sortedUsers, user)
                        }</span>
                }

                // 分页处理
                <span class="cov0" title="0">users = sortedUsers
                if offset &lt; len(users) </span><span class="cov0" title="0">{
                        end := offset + limit
                        if end &gt; len(users) </span><span class="cov0" title="0">{
                                end = len(users)
                        }</span>
                        <span class="cov0" title="0">users = users[offset:end]</span>
                } else<span class="cov0" title="0"> {
                        users = []struct {
                                douyin.DyUser
                                SysUserName   string `json:"sysUserName"`
                                PhoneUserName string `json:"phoneUserName"`
                                PhoneOperator string `json:"phoneOperator"`
                        }{}
                }</span>
        } else<span class="cov0" title="0"> {
                // 没有自定义排序，使用默认排序
                err = db.Select(
                        "dy_user.*, sys_users.nick_name as sys_user_name, phone_balance.real_name as phone_user_name, phone_balance.operator_type as phone_operator").
                        Joins("left join sys_users on dy_user.sys_user_id = sys_users.id").
                        Joins("left join phone_balance on dy_user.bind_phone = phone_balance.phone_number AND phone_balance.deleted_at IS NULL").
                        Limit(limit).Offset(offset).Order("dy_user.created_at desc").Find(&amp;users).Error
        }</span>

        // 将结果转换为 DyUser 切片
        <span class="cov0" title="0">list = make([]douyin.DyUser, len(users))
        for i, user := range users </span><span class="cov0" title="0">{
                list[i] = user.DyUser
                var sysUserName string
                if user.SysUserName != "" </span><span class="cov0" title="0">{
                        sysUserName = user.SysUserName
                }</span> else<span class="cov0" title="0"> {
                        sysUserName = "未实名"
                }</span>
                <span class="cov0" title="0">list[i].SysUserName = sysUserName
                list[i].PhoneUserName = user.PhoneUserName
                var phoneOperator string
                switch user.PhoneOperator </span>{
                case "mobile":<span class="cov0" title="0">
                        phoneOperator = "中国移动"</span>
                case "unicom":<span class="cov0" title="0">
                        phoneOperator = "中国联通"</span>
                case "telecom":<span class="cov0" title="0">
                        phoneOperator = "中国电信"</span>
                case "35internet":<span class="cov0" title="0">
                        phoneOperator = "三五互联"</span>
                default:<span class="cov0" title="0">
                        phoneOperator = "未知"</span>
                }
                <span class="cov0" title="0">list[i].PhoneOperator = phoneOperator</span>
        }

        <span class="cov0" title="0">return list, total, err</span>
}

func (s *DyUserService) GetAllUsers(categoryId int, sysUserIds []uint) (users []douyin.DyUser, err error) <span class="cov0" title="0">{
        db := global.GVA_DB.Model(&amp;douyin.DyUser{})

        // 构建查询条件
        if categoryId != 0 </span><span class="cov0" title="0">{
                db = db.Where("category_id = ?", categoryId)
        }</span>
        // 添加sys_user_id查询条件，只能查询当前用户及其下级用户的抖音用户
        <span class="cov0" title="0">db = db.Where("sys_user_id IN ?", sysUserIds)

        err = db.Select("dy_user.* ").
                Find(&amp;users).Error

        return</span>
}

// DeleteUser 删除抖音用户
func (s *DyUserService) DeleteUser(id uint) error <span class="cov0" title="0">{
        // 开启事务
        tx := global.GVA_DB.Begin()

        // 查询用户信息
        var user douyin.DyUser
        if err := tx.First(&amp;user, id).Error; err != nil </span><span class="cov0" title="0">{
                tx.Rollback()
                return err
        }</span>

        // 如果用户绑定了设备，解除绑定
        <span class="cov0" title="0">if user.BindDevice != "" </span><span class="cov0" title="0">{
                // 解析设备信息
                var deviceInfo map[string]interface{}
                if err := json.Unmarshal([]byte(user.BindDevice), &amp;deviceInfo); err != nil </span><span class="cov0" title="0">{
                        tx.Rollback()
                        return err
                }</span>

                // 获取设备ID
                <span class="cov0" title="0">if deviceID, ok := deviceInfo["id"].(float64); ok </span><span class="cov0" title="0">{
                        // 更新设备表，解除绑定
                        if err := tx.Model(&amp;douyin.DeviceInfo{}).
                                Where("id = ?", uint(deviceID)).
                                Update("dy_user_id", nil).Error; err != nil </span><span class="cov0" title="0">{
                                tx.Rollback()
                                return err
                        }</span>
                }
        }

        // 删除用户
        <span class="cov0" title="0">if err := tx.Delete(&amp;douyin.DyUser{}, "id = ?", id).Error; err != nil </span><span class="cov0" title="0">{
                tx.Rollback()
                return err
        }</span>

        // 提交事务
        <span class="cov0" title="0">if err := tx.Commit().Error; err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        // 如果用户绑定了IP，更新IP池中的用户计数
        <span class="cov0" title="0">if user.BindIP != "" </span><span class="cov0" title="0">{
                var ipInfo douyin.IpPool
                if err := global.GVA_DB.Where("ip = ?", user.BindIP).First(&amp;ipInfo).Error; err == nil </span><span class="cov0" title="0">{
                        // 更新IP池中的用户计数
                        dyIPService := &amp;DyIPService{}
                        if err := dyIPService.UpdateUserCount(ipInfo.ID); err != nil </span><span class="cov0" title="0">{
                                return err
                        }</span>
                }
        }

        // 删除用户的自动发布任务
        <span class="cov0" title="0">if err := global.GVA_DB.Where("dy_user_id =?", id).Delete(&amp;creative.AutoPublishVideo{}).Error; err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        <span class="cov0" title="0">return nil</span>
}

// GetUserByToken 根据token获取抖音用户信息
func (s *DyUserService) GetUserByToken(token string) (*douyin.DyUser, error) <span class="cov0" title="0">{
        var user douyin.DyUser
        err := global.GVA_DB.Where("im_token = ?", token).First(&amp;user).Error
        return &amp;user, err
}</span>

// ToggleProductEnabled 切换选品状态
func (s *DyUserService) ToggleProductEnabled(id uint, enabled bool) error <span class="cov0" title="0">{
        return global.GVA_DB.Model(&amp;douyin.DyUser{}).Where("id = ?", id).Update("is_product_enabled", enabled).Error
}</span>

// 增加一个绑定用户手机号

// UpdateUserIP 更新用户绑定IP
func (s *DyUserService) UpdateUserIP(id uint, bindIP string) error <span class="cov0" title="0">{
        // 开启事务
        return global.GVA_DB.Transaction(func(tx *gorm.DB) error </span><span class="cov0" title="0">{
                // 获取用户当前绑定的IP
                var user douyin.DyUser
                if err := tx.Where("id = ?", id).First(&amp;user).Error; err != nil </span><span class="cov0" title="0">{
                        return err
                }</span>

                // 更新用户绑定的IP
                <span class="cov0" title="0">if err := tx.Model(&amp;douyin.DyUser{}).Where("id = ?", id).Update("bind_ip", bindIP).Error; err != nil </span><span class="cov0" title="0">{
                        return err
                }</span>

                // 如果原来有绑定IP，更新原IP的用户计数
                <span class="cov0" title="0">if user.BindIP != "" </span><span class="cov0" title="0">{
                        var oldIP douyin.IpPool
                        if err := tx.Where("ip = ?", user.BindIP).First(&amp;oldIP).Error; err == nil </span><span class="cov0" title="0">{
                                // 更新原IP的用户计数
                                dyIPService := &amp;DyIPService{}
                                if err := dyIPService.UpdateUserCount(oldIP.ID); err != nil </span><span class="cov0" title="0">{
                                        return err
                                }</span>
                        }
                }

                // 如果新绑定了IP，更新新IP的用户计数
                <span class="cov0" title="0">if bindIP != "" </span><span class="cov0" title="0">{
                        var newIP douyin.IpPool
                        if err := tx.Where("ip = ?", bindIP).First(&amp;newIP).Error; err == nil </span><span class="cov0" title="0">{
                                // 更新新IP的用户计数
                                dyIPService := &amp;DyIPService{}
                                if err := dyIPService.UpdateUserCount(newIP.ID); err != nil </span><span class="cov0" title="0">{
                                        return err
                                }</span>
                        }
                }

                <span class="cov0" title="0">return nil</span>
        })
}

// 绑定信息
func (s *DyUserService) BindInfo(id uint, realName string, phone string) error <span class="cov0" title="0">{
        if realName != "" </span><span class="cov0" title="0">{
                return global.GVA_DB.Model(&amp;douyin.DyUser{}).Where("id = ?", id).Update("real_name", realName).Error
        }</span>
        <span class="cov0" title="0">if phone != "" </span><span class="cov0" title="0">{
                // 验证手机号是否存在于 phone_balance 表中且未删除
                var count int64
                err := global.GVA_DB.Model(&amp;douyin.PhoneBalance{}).
                        Where("phone_number = ? AND deleted_at IS NULL", phone).
                        Count(&amp;count).Error
                if err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("验证手机号失败: %w", err)
                }</span>
                <span class="cov0" title="0">if count == 0 </span><span class="cov0" title="0">{
                        return fmt.Errorf("该手机号不在话费余额表中或已被删除")
                }</span>

                // 验证手机号是否已被其他用户绑定
                <span class="cov0" title="0">var existingUser douyin.DyUser
                err = global.GVA_DB.Where("bind_phone = ? AND id != ?", phone, id).First(&amp;existingUser).Error
                if err == nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("该手机号已被其他用户绑定")
                }</span>

                <span class="cov0" title="0">return global.GVA_DB.Model(&amp;douyin.DyUser{}).Where("id = ?", id).Update("bind_phone", phone).Error</span>
        }
        <span class="cov0" title="0">return nil</span>
}

// BindDevice 绑定设备到用户
func (s *DyUserService) BindDevice(userID uint, deviceID uint) error <span class="cov0" title="0">{
        // 获取设备信息
        var device douyin.DeviceInfo
        if err := global.GVA_DB.First(&amp;device, deviceID).Error; err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        // 更新用户的设备信息
        <span class="cov0" title="0">return global.GVA_DB.Model(&amp;douyin.DyUser{}).Where("id = ?", userID).Updates(map[string]interface{}{
                "bind_device": true,
                "device_id":   deviceID,
        }).Error</span>
}

// GetUserByID 根据ID获取用户信息
func (s *DyUserService) GetUserByID(id uint) (*douyin.DyUser, error) <span class="cov0" title="0">{
        var user douyin.DyUser
        err := global.GVA_DB.Where("id = ?", id).First(&amp;user).Error
        return &amp;user, err
}</span>

func (s *DyUserService) GetUserByUniqueId(uniqueId string) (*douyin.DyUser, error) <span class="cov0" title="0">{
        var user douyin.DyUser
        err := global.GVA_DB.Where("unique_id = ?", uniqueId).First(&amp;user).Error
        return &amp;user, err
}</span>

// UpdateUserInfo 更新用户信息
func (s *DyUserService) UpdateUserInfo(id uint, updateData map[string]interface{}) error <span class="cov0" title="0">{
        return global.GVA_DB.Model(&amp;douyin.DyUser{}).Where("id = ?", id).Updates(updateData).Error
}</span>

// GetAwemeCookie 获取用户抖音号cookie
func (s *DyUserService) GetAwemeCookie(uniqueId string) (string, error) <span class="cov0" title="0">{
        cookieKey := fmt.Sprintf(douyin.MoreApiCookieKey, uniqueId)
        cookie, err := global.GVA_REDIS.Get(context.Background(), cookieKey).Result()
        return cookie, err
}</span>

// SetAwemeCookie 设置用户cookie
func (s *DyUserService) SetAwemeCookie(uniqueId, cookie string) error <span class="cov0" title="0">{
        cookieKey := fmt.Sprintf(douyin.MoreApiCookieKey, uniqueId)
        return global.GVA_REDIS.Set(context.Background(), cookieKey, cookie, -1).Err()
}</span>

// 获取专门用于请求More API的Cookie
func (s *DyUserService) GetAwemeDefaultCookie() (string, error) <span class="cov0" title="0">{
        return s.GetAwemeCookie(douyin.MoreApiRequestUniqueId)
}</span>

// UpdateCategory 更新用户分类
func (s *DyUserService) UpdateCategory(id uint, categoryId uint) error <span class="cov0" title="0">{
        return global.GVA_DB.Model(&amp;douyin.DyUser{}).Where("id = ?", id).Update("category_id", categoryId).Error
}</span>

// UpdateUserRemark 更新用户备注
func (s *DyUserService) UpdateUserRemark(req request.UpdateUserRemarkRequest) error <span class="cov0" title="0">{
        return global.GVA_DB.Model(&amp;douyin.DyUser{}).Where("id = ?", req.ID).Update("remark", req.Remark).Error
}</span>

// SearchUserByUniqueId 根据抖音号搜索用户
func (s *DyUserService) SearchUserByUniqueId(uniqueId string) (list *[]douyin.DyUser, err error) <span class="cov0" title="0">{
        db := global.GVA_DB.Model(&amp;douyin.DyUser{})
        // 添加模糊查询条件和未删除条件
        db = db.Where("unique_id LIKE ? AND deleted_at IS NULL", uniqueId+"%")
        // 获取符合条件的记录
        err = db.Find(&amp;list).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>
        <span class="cov0" title="0">return list, nil</span>
}

func (s *DyUserService) SearchUserByPort(port string) (list *[]douyin.DyUser, err error) <span class="cov0" title="0">{
        db := global.GVA_DB.Model(&amp;douyin.DyUser{})
        // 添加模糊查询条件和未删除条件
        db = db.Where("bind_ip LIKE ? AND deleted_at IS NULL", "%"+port)
        // 获取符合条件的记录
        err = db.Find(&amp;list).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>
        <span class="cov0" title="0">return list, nil</span>
}

func (s *DyUserService) SearchUserByMac(mac string) (list *[]douyin.IpPool, err error) <span class="cov0" title="0">{
        db := global.GVA_DB.Model(&amp;douyin.IpPool{})
        // 添加模糊查询条件
        db = db.Where("mac_addresses LIKE ?", mac+"%")
        // 获取符合条件的记录
        err = db.Find(&amp;list).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>
        <span class="cov0" title="0">return list, nil</span>
}

// SaveUserSort 保存用户排序到Redis
func (s *DyUserService) SaveUserSort(sysUserId uint, categoryId *uint, userIds []uint) error <span class="cov0" title="0">{
        // 构建Redis key，按用户和分类存储
        key := fmt.Sprintf("user_sort:%d", sysUserId)
        if categoryId != nil &amp;&amp; *categoryId &gt; 0 </span><span class="cov0" title="0">{
                key = fmt.Sprintf("%s:category:%d", key, *categoryId)
        }</span> else<span class="cov0" title="0"> {
                key = fmt.Sprintf("%s:category:0", key)
        }</span>

        // 将用户ID列表转换为JSON字符串存储
        <span class="cov0" title="0">userIdsJson, err := json.Marshal(userIds)
        if err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        // 存储到Redis，设置过期时间为30天
        <span class="cov0" title="0">return global.GVA_REDIS.Set(context.Background(), key, string(userIdsJson), 30*24*time.Hour).Err()</span>
}

// GetUserSort 从Redis获取用户排序
func (s *DyUserService) GetUserSort(sysUserId uint, categoryId *uint) ([]uint, error) <span class="cov0" title="0">{
        // 构建Redis key
        key := fmt.Sprintf("user_sort:%d", sysUserId)
        if categoryId != nil &amp;&amp; *categoryId &gt; 0 </span><span class="cov0" title="0">{
                key = fmt.Sprintf("%s:category:%d", key, *categoryId)
        }</span> else<span class="cov0" title="0"> {
                key = fmt.Sprintf("%s:category:0", key)
        }</span>

        // 从Redis获取数据
        <span class="cov0" title="0">result, err := global.GVA_REDIS.Get(context.Background(), key).Result()
        if err != nil </span><span class="cov0" title="0">{
                // 如果没有找到排序数据，返回空数组
                if err.Error() == "redis: nil" </span><span class="cov0" title="0">{
                        return []uint{}, nil
                }</span>
                <span class="cov0" title="0">return nil, err</span>
        }

        // 解析JSON数据
        <span class="cov0" title="0">var userIds []uint
        if err := json.Unmarshal([]byte(result), &amp;userIds); err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        <span class="cov0" title="0">return userIds, nil</span>
}
</pre>
		
		<pre class="file" id="file12" style="display: none">package douyin

import (
        "errors"

        "github.com/flipped-aurora/gin-vue-admin/server/global"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
        "gorm.io/gorm"
)

type DyUserCategoryService struct{}

// AddCategory 创建/更新用户分类
func (a *DyUserCategoryService) AddCategory(req *douyin.DyUserCategory) (err error) <span class="cov0" title="0">{
        // 检查是否已存在相同名称的分类
        if (!errors.Is(global.GVA_DB.Take(&amp;douyin.DyUserCategory{}, "name = ? and pid = ? and user_id = ?", req.Name, req.Pid, req.UserID).Error, gorm.ErrRecordNotFound)) </span><span class="cov0" title="0">{
                return errors.New("分类名称已存在")
        }</span>
        <span class="cov0" title="0">if req.ID &gt; 0 </span><span class="cov0" title="0">{
                if err = global.GVA_DB.Model(&amp;douyin.DyUserCategory{}).Where("id = ? and user_id = ?", req.ID, req.UserID).Updates(&amp;douyin.DyUserCategory{
                        Name: req.Name,
                        Pid:  req.Pid,
                }).Error; err != nil </span><span class="cov0" title="0">{
                        return err
                }</span>
        } else<span class="cov0" title="0"> {
                if err = global.GVA_DB.Create(&amp;douyin.DyUserCategory{
                        Name:   req.Name,
                        Pid:    req.Pid,
                        UserID: req.UserID,
                }).Error; err != nil </span><span class="cov0" title="0">{
                        return err
                }</span>
        }
        <span class="cov0" title="0">return nil</span>
}

// DeleteCategory 删除分类
func (a *DyUserCategoryService) DeleteCategory(id *int, userID uint) error <span class="cov0" title="0">{
        var childCount int64
        global.GVA_DB.Model(&amp;douyin.DyUserCategory{}).Where("pid = ? and user_id = ?", id, userID).Count(&amp;childCount)
        if childCount &gt; 0 </span><span class="cov0" title="0">{
                return errors.New("请先删除子级")
        }</span>
        <span class="cov0" title="0">return global.GVA_DB.Where("id = ? and user_id = ?", id, userID).Unscoped().Delete(&amp;douyin.DyUserCategory{}).Error</span>
}

// GetCategoryList 分类列表
func (a *DyUserCategoryService) GetCategoryList(userID uint) (res []*douyin.DyUserCategory, err error) <span class="cov0" title="0">{
        var fileLists []douyin.DyUserCategory
        err = global.GVA_DB.Model(&amp;douyin.DyUserCategory{}).Where("user_id = ?", userID).Find(&amp;fileLists).Error
        if err != nil </span><span class="cov0" title="0">{
                return res, err
        }</span>
        <span class="cov0" title="0">return a.getChildrenList(fileLists, 0), nil</span>
}

// getChildrenList 子类
func (a *DyUserCategoryService) getChildrenList(categories []douyin.DyUserCategory, parentID uint) []*douyin.DyUserCategory <span class="cov0" title="0">{
        var tree []*douyin.DyUserCategory
        for _, category := range categories </span><span class="cov0" title="0">{
                if category.Pid == parentID </span><span class="cov0" title="0">{
                        category.Children = a.getChildrenList(categories, category.ID)
                        tree = append(tree, &amp;category)
                }</span>
        }
        <span class="cov0" title="0">return tree</span>
}
</pre>
		
		<pre class="file" id="file13" style="display: none">package douyin

import (
        "fmt"
        "sync"

        "github.com/flipped-aurora/gin-vue-admin/server/global"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/response"
)

type FlameApiService struct {
        client *DyHttpClient
        once   sync.Once
}

var FlameApiServiceApp = new(FlameApiService)

func (s *FlameApiService) getClient() *DyHttpClient <span class="cov0" title="0">{
        s.once.Do(func() </span><span class="cov0" title="0">{
                s.client = NewDyHttpClient(global.GVA_CONFIG.Douyin.BaseURL)
        }</span>)
        <span class="cov0" title="0">return s.client</span>
}

func (s *FlameApiService) updateReq(req map[string]interface{}) error <span class="cov0" title="0">{
        // 获取用户信息，检查是否配置了IP
        flameUser, err := FlameUserServiceApp.GetUserByToken(req["token"].(string))
        if err != nil </span><span class="cov0" title="0">{
                return err
        }</span>
        <span class="cov0" title="0">if flameUser.BindIP == "" </span><span class="cov0" title="0">{
                return fmt.Errorf("用户未配置IP地址，请先在用户管理中配置IP")
        }</span>
        <span class="cov0" title="0">req["proxy_ip"] = flameUser.BindIP
        req["device_id"] = flameUser.Did
        return nil</span>
}

// CollectIncomeBubble 领取ALL收到的火苗
func (s *FlameApiService) CollectIncomeBubble(
        req request.CollectIncomeBubbleRequest,
) (response.CollectIncomeBubbleResponse, error) <span class="cov0" title="0">{
        reqMap := map[string]interface{}{
                "token": req.Token,
        }
        if err := s.updateReq(reqMap); err != nil </span><span class="cov0" title="0">{
                return response.CollectIncomeBubbleResponse{}, err
        }</span>

        <span class="cov0" title="0">var result response.CollectIncomeBubbleResponse
        if err := s.getClient().DoRequest("POST", "/api/hs/collect_income_bubble", reqMap, &amp;result); err != nil </span><span class="cov0" title="0">{
                return response.CollectIncomeBubbleResponse{}, err
        }</span>
        <span class="cov0" title="0">return result, nil</span>
}

// CollectFlamePlayer 火苗玩家采集(week榜）
func (s *FlameApiService) CollectFlamePlayer(
        req request.CollectFlamePlayerRequest,
) (resp response.CollectFlamePlayerResponse, continueCollect bool, err error) <span class="cov0" title="0">{
        reqMap := map[string]interface{}{
                "token":   req.Token,
                "user_id": req.UserID,
                "offset":  req.Offset,
        }
        if err := s.updateReq(reqMap); err != nil </span><span class="cov0" title="0">{
                return response.CollectFlamePlayerResponse{}, false, err
        }</span>

        <span class="cov0" title="0">var result response.CollectFlamePlayerResponse
        if err := s.getClient().DoRequest("POST", "/api/hs/flame_rank", reqMap, &amp;result); err != nil </span><span class="cov0" title="0">{
                return response.CollectFlamePlayerResponse{}, true, err
        }</span>
        <span class="cov0" title="0">return result, true, nil</span>
}

// SendFlame 发送火苗
func (s *FlameApiService) SendFlame(req request.SendFlameRequest) (response.SendFlameResponse, error) <span class="cov0" title="0">{
        reqMap := map[string]interface{}{
                "token":       req.Token,
                "to_user_id":  req.ToUserID,
                "flame_count": req.FlameCount,
        }
        if err := s.updateReq(reqMap); err != nil </span><span class="cov0" title="0">{
                return response.SendFlameResponse{}, err
        }</span>

        <span class="cov0" title="0">var result response.SendFlameResponse
        if err := s.getClient().DoRequest("POST", "/api/hs/flame_send", reqMap, &amp;result); err != nil </span><span class="cov0" title="0">{
                return response.SendFlameResponse{}, err
        }</span>
        <span class="cov0" title="0">return result, nil</span>
}

// GetFlameBalance 获取账号火苗余额
func (s *FlameApiService) GetFlameBalance(req request.GetFlameBalanceRequest) (response.GetFlameBalanceResponse, error) <span class="cov0" title="0">{
        reqMap := map[string]interface{}{
                "token": req.Token,
        }
        if err := s.updateReq(reqMap); err != nil </span><span class="cov0" title="0">{
                return response.GetFlameBalanceResponse{}, err
        }</span>

        <span class="cov0" title="0">var result response.GetFlameBalanceResponse
        if err := s.getClient().DoRequest("POST", "/api/hs/flame_management", reqMap, &amp;result); err != nil </span><span class="cov0" title="0">{
                return response.GetFlameBalanceResponse{}, err
        }</span>
        <span class="cov0" title="0">return result, nil</span>
}
</pre>
		
		<pre class="file" id="file14" style="display: none">package douyin

import (
        "time"

        "github.com/flipped-aurora/gin-vue-admin/server/global"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/response"
        "go.uber.org/zap"
        "gorm.io/gorm"
)

type FlamePlayerService struct{}

var FlamePlayerServiceApp = new(FlamePlayerService)

// SaveFlamePlayer 保存火苗玩家数据
func (s *FlamePlayerService) SaveFlamePlayer(playerData response.FlamePlayerData) error <span class="cov0" title="0">{
        player := douyin.FlamePlayer{
                UID:                 playerData.UID,
                Nickname:            playerData.Nickname,
                ShortId:             playerData.ShortId,
                Gender:              playerData.Gender,
                City:                playerData.City,
                BirthdayDescription: playerData.BirthdayDescription,
                IsFollowing:         playerData.IsFollowing,
                IsFollower:          playerData.IsFollower,
                Level:               playerData.Level,
                FollowerCount:       playerData.FollowerCount,
                FollowingCount:      playerData.FollowingCount,
                FavoriteItemCount:   playerData.FavoriteItemCount,
                ItemCount:           playerData.ItemCount,
                Rank:                playerData.Rank,
                Flame:               playerData.Flame,
        }

        // 检查是否已存在该玩家
        var existingPlayer douyin.FlamePlayer
        err := global.GVA_DB.Where("uid = ?", playerData.UID).First(&amp;existingPlayer).Error
        if err == nil </span><span class="cov0" title="0">{
                // 已存在，更新数据
                existingPlayer.Nickname = player.Nickname
                existingPlayer.ShortId = player.ShortId
                existingPlayer.Gender = player.Gender
                existingPlayer.City = player.City
                existingPlayer.BirthdayDescription = player.BirthdayDescription
                existingPlayer.IsFollowing = player.IsFollowing
                existingPlayer.IsFollower = player.IsFollower
                existingPlayer.Level = player.Level
                existingPlayer.FollowerCount = player.FollowerCount
                existingPlayer.FollowingCount = player.FollowingCount
                existingPlayer.FavoriteItemCount = player.FavoriteItemCount
                existingPlayer.ItemCount = player.ItemCount
                existingPlayer.Rank = player.Rank
                existingPlayer.Flame = player.Flame
                return global.GVA_DB.Save(&amp;existingPlayer).Error
        }</span>
        // 不存在，创建新记录
        <span class="cov0" title="0">return global.GVA_DB.Create(&amp;player).Error</span>
}

// GetPlayerList 获取火苗玩家列表
func (s *FlamePlayerService) GetPlayerList(info request.FlamePlayerSearch) (list []douyin.FlamePlayer, total int64, err error) <span class="cov0" title="0">{
        limit := info.PageSize
        offset := info.PageSize * (info.Page - 1)

        // 使用公共方法构建查询条件
        db := s.buildPlayerQuery(info)
        var flamePlayers []douyin.FlamePlayer

        err = db.Count(&amp;total).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, 0, err
        }</span>

        // 添加按ID正序排序
        <span class="cov0" title="0">err = db.Limit(limit).Offset(offset).Order("id asc").Find(&amp;flamePlayers).Error

        return flamePlayers, total, err</span>
}

// buildPlayerQuery 构建玩家查询条件
func (s *FlamePlayerService) buildPlayerQuery(info request.FlamePlayerSearch) *gorm.DB <span class="cov0" title="0">{
        db := global.GVA_DB.Model(&amp;douyin.FlamePlayer{})

        if info.Nickname != "" </span><span class="cov0" title="0">{
                db = db.Where("nickname LIKE ?", "%"+info.Nickname+"%")
        }</span>
        <span class="cov0" title="0">if info.ShortId != "" </span><span class="cov0" title="0">{
                db = db.Where("short_id LIKE ?", "%"+info.ShortId+"%")
        }</span>
        <span class="cov0" title="0">if info.City != "" </span><span class="cov0" title="0">{
                db = db.Where("city LIKE ?", "%"+info.City+"%")
        }</span>
        <span class="cov0" title="0">if info.Gender &gt; -1 </span><span class="cov0" title="0">{
                db = db.Where("gender = ?", info.Gender)
        }</span>
        <span class="cov0" title="0">if len(info.BirthdayDescription) &gt; 0 </span><span class="cov0" title="0">{
                db = db.Where("birthday_description IN ?", info.BirthdayDescription)
        }</span>
        <span class="cov0" title="0">if info.Level &gt; 0 </span><span class="cov0" title="0">{
                db = db.Where("level = ?", info.Level)
        }</span>
        <span class="cov0" title="0">if info.IsFollowing </span><span class="cov0" title="0">{
                db = db.Where("is_following = ?", true)
        }</span>
        <span class="cov0" title="0">if info.IsFollower </span><span class="cov0" title="0">{
                db = db.Where("is_follower = ?", true)
        }</span>
        <span class="cov0" title="0">if info.FollowerCountMin &gt; 0 </span><span class="cov0" title="0">{
                db = db.Where("follower_count &gt;= ?", info.FollowerCountMin)
        }</span>
        <span class="cov0" title="0">if info.FollowerCountMax &gt; 0 </span><span class="cov0" title="0">{
                db = db.Where("follower_count &lt;= ?", info.FollowerCountMax)
        }</span>
        <span class="cov0" title="0">if info.FollowingCountMin &gt; 0 </span><span class="cov0" title="0">{
                db = db.Where("following_count &gt;= ?", info.FollowingCountMin)
        }</span>
        <span class="cov0" title="0">if info.FollowingCountMax &gt; 0 </span><span class="cov0" title="0">{
                db = db.Where("following_count &lt;= ?", info.FollowingCountMax)
        }</span>
        <span class="cov0" title="0">if info.FavoriteItemCountMin &gt; 0 </span><span class="cov0" title="0">{
                db = db.Where("favorite_item_count &gt;= ?", info.FavoriteItemCountMin)
        }</span>
        <span class="cov0" title="0">if info.FavoriteItemCountMax &gt; 0 </span><span class="cov0" title="0">{
                db = db.Where("favorite_item_count &lt;= ?", info.FavoriteItemCountMax)
        }</span>
        <span class="cov0" title="0">if info.ItemCountMin &gt; 0 </span><span class="cov0" title="0">{
                db = db.Where("item_count &gt;= ?", info.ItemCountMin)
        }</span>
        <span class="cov0" title="0">if info.ItemCountMax &gt; 0 </span><span class="cov0" title="0">{
                db = db.Where("item_count &lt;= ?", info.ItemCountMax)
        }</span>
        <span class="cov0" title="0">if info.FlameMin &gt; 0 </span><span class="cov0" title="0">{
                db = db.Where("flame &gt;= ?", info.FlameMin)
        }</span>
        <span class="cov0" title="0">if info.FlameMax &gt; 0 </span><span class="cov0" title="0">{
                db = db.Where("flame &lt;= ?", info.FlameMax)
        }</span>
        <span class="cov0" title="0">if info.IsCollected </span><span class="cov0" title="0">{
                db = db.Where("is_collected = ?", info.IsCollected)
        }</span>

        // 处理 IsSentFlame 参数，只有当它被设置时才添加条件
        <span class="cov0" title="0">if info.IsSentFlame != nil </span><span class="cov0" title="0">{
                db = db.Where("is_sent_flame = ?", *info.IsSentFlame)
        }</span>

        <span class="cov0" title="0">return db</span>
}

// GetPlayersBySearchCond 根据搜索条件获取玩家列表（不分页）
func (s *FlamePlayerService) GetPlayersBySearchCond(info request.FlamePlayerSearch) ([]douyin.FlamePlayer, error) <span class="cov0" title="0">{
        // 使用公共方法构建查询条件
        db := s.buildPlayerQuery(info)
        var flamePlayers []douyin.FlamePlayer

        err := db.Find(&amp;flamePlayers).Error
        return flamePlayers, err
}</span>

// BatchSendFlame 批量赠送火苗
func (s *FlamePlayerService) BatchSendFlame(
        req request.BatchSendFlameRequest,
        flameApiService *FlameApiService,
) (successCount int, failCount int, err error) <span class="cov0" title="0">{
        // 根据搜索条件获取玩家列表（不分页）
        list, err := s.GetPlayersBySearchCond(req.SearchCond)
        if err != nil </span><span class="cov0" title="0">{
                return 0, 0, err
        }</span>

        // 如果设置了最大赠送人数且大于0，则限制赠送人数
        <span class="cov0" title="0">if req.SearchCond.MaxSendCount &gt; 0 &amp;&amp; len(list) &gt; req.SearchCond.MaxSendCount </span><span class="cov0" title="0">{
                list = list[:req.SearchCond.MaxSendCount]
        }</span>

        // 批量赠送火苗
        <span class="cov0" title="0">for _, player := range list </span><span class="cov0" title="0">{
                // 如果已经赠送过火苗，则跳过
                if player.IsSentFlame </span><span class="cov0" title="0">{
                        continue</span>
                }

                // 构造赠送火苗请求
                <span class="cov0" title="0">sendReq := request.SendFlameRequest{
                        Token:      req.Token,
                        ToUserID:   player.UID,
                        FlameCount: req.FlameCount,
                }

                // 调用赠送火苗接口
                _, sendErr := flameApiService.SendFlame(sendReq)
                if sendErr != nil </span><span class="cov0" title="0">{
                        failCount++
                        global.GVA_LOG.Error("赠送火苗失败", zap.Error(sendErr), zap.String("uid", player.UID))
                        continue</span>
                }

                // 更新玩家的IsSentFlame状态
                <span class="cov0" title="0">player.IsSentFlame = true
                if updateErr := global.GVA_DB.Model(&amp;player).Update("is_sent_flame", true).Error; updateErr != nil </span><span class="cov0" title="0">{
                        global.GVA_LOG.Error("更新玩家赠送状态失败", zap.Error(updateErr), zap.String("uid", player.UID))
                }</span>

                <span class="cov0" title="0">successCount++</span>
        }

        <span class="cov0" title="0">return successCount, failCount, nil</span>
}

// BatchSendFlameWithRateLimit 带有频率限制的批量赠送火苗
func (s *FlamePlayerService) BatchSendFlameWithRateLimit(
        req request.BatchSendFlameRequest,
        flameApiService *FlameApiService,
        rateLimit int, // 频率限制，单位为秒
) (successCount int, failCount int, err error) <span class="cov0" title="0">{
        // 根据搜索条件获取玩家列表（不分页）
        list, err := s.GetPlayersBySearchCond(req.SearchCond)
        if err != nil </span><span class="cov0" title="0">{
                return 0, 0, err
        }</span>

        // 如果设置了最大赠送人数且大于0，则限制赠送人数
        <span class="cov0" title="0">if req.SearchCond.MaxSendCount &gt; 0 &amp;&amp; len(list) &gt; req.SearchCond.MaxSendCount </span><span class="cov0" title="0">{
                list = list[:req.SearchCond.MaxSendCount]
        }</span>

        // 批量赠送火苗
        <span class="cov0" title="0">for i, player := range list </span><span class="cov0" title="0">{
                // 如果已经赠送过火苗，则跳过
                if player.IsSentFlame </span><span class="cov0" title="0">{
                        continue</span>
                }

                // 构造赠送火苗请求
                <span class="cov0" title="0">sendReq := request.SendFlameRequest{
                        Token:      req.Token,
                        ToUserID:   player.UID,
                        FlameCount: req.FlameCount,
                }

                // 调用赠送火苗接口
                _, sendErr := flameApiService.SendFlame(sendReq)
                if sendErr != nil </span><span class="cov0" title="0">{
                        failCount++
                        global.GVA_LOG.Error("赠送火苗失败", zap.Error(sendErr), zap.String("uid", player.UID))
                }</span> else<span class="cov0" title="0"> {
                        // 更新玩家的IsSentFlame状态
                        player.IsSentFlame = true
                        if updateErr := global.GVA_DB.Model(&amp;player).Update("is_sent_flame", true).Error; updateErr != nil </span><span class="cov0" title="0">{
                                global.GVA_LOG.Error("更新玩家赠送状态失败", zap.Error(updateErr), zap.String("uid", player.UID))
                        }</span>
                        <span class="cov0" title="0">successCount++</span>
                }

                // 添加频率限制，每次赠送后等待指定的秒数（无论成功还是失败）
                // 只有在不是最后一个玩家时才等待
                <span class="cov0" title="0">if rateLimit &gt; 0 &amp;&amp; i &lt; len(list)-1 </span><span class="cov0" title="0">{
                        time.Sleep(time.Duration(rateLimit) * time.Second)
                }</span>
        }

        <span class="cov0" title="0">return successCount, failCount, nil</span>
}
</pre>
		
		<pre class="file" id="file15" style="display: none">package douyin

import (
        "github.com/flipped-aurora/gin-vue-admin/server/global"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
)

type FlamePlayerCollectionService struct{}

var FlamePlayerCollectionServiceApp = new(FlamePlayerCollectionService)

// CreateCollection 创建采集记录
func (s *FlamePlayerCollectionService) CreateCollection(collection *douyin.FlamePlayerCollection) error <span class="cov0" title="0">{
        return global.GVA_DB.Create(collection).Error
}</span>

// UpdateCollection 更新采集记录
func (s *FlamePlayerCollectionService) UpdateCollection(id uint, updates map[string]interface{}) error <span class="cov0" title="0">{
        return global.GVA_DB.Model(&amp;douyin.FlamePlayerCollection{}).Where("id = ?", id).Updates(updates).Error
}</span>

// GetCollectionList 获取采集记录列表
func (s *FlamePlayerCollectionService) GetCollectionList(
        info request.FlamePlayerCollectionSearch,
) (list interface{}, total int64, err error) <span class="cov0" title="0">{
        // 先更新超过5分钟未更新且状态为"进行中"的记录状态为"已中断"
        updateSQL := `
                UPDATE flame_player_collection 
                SET status = 3 
                WHERE status = 1 
                AND updated_at &lt; DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        `
        global.GVA_DB.Exec(updateSQL)

        limit := info.PageSize
        offset := info.PageSize * (info.Page - 1)

        // 定义结果结构体
        type CollectionWithNames struct {
                douyin.FlamePlayerCollection
                SysUserName   string `json:"sysUserName" gorm:"column:sys_user_name"`
                FlameUserName string `json:"flameUserName" gorm:"column:flame_user_name"`
        }

        // 构建基础查询
        db := global.GVA_DB.Model(&amp;douyin.FlamePlayerCollection{})

        // 应用过滤条件
        if info.SysUserId &gt; 0 </span><span class="cov0" title="0">{
                db = db.Where("flame_player_collection.sys_user_id = ?", info.SysUserId)
        }</span>
        <span class="cov0" title="0">if info.FlameUserId &gt; 0 </span><span class="cov0" title="0">{
                db = db.Where("flame_player_collection.flame_user_id = ?", info.FlameUserId)
        }</span>
        <span class="cov0" title="0">if info.Status &gt; 0 </span><span class="cov0" title="0">{
                db = db.Where("flame_player_collection.status = ?", info.Status)
        }</span>

        // 计算总数 - 这里不包含FlameUserName筛选，因为它需要联表查询
        <span class="cov0" title="0">var count int64
        err = db.Count(&amp;count).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, 0, err
        }</span>

        // 构建联表查询SQL
        <span class="cov0" title="0">query := `
                SELECT 
                        fpc.*,
                        su.nick_name AS sys_user_name,
                        fu.nickname AS flame_user_name
                FROM 
                        flame_player_collection fpc
                LEFT JOIN 
                        sys_users su ON fpc.sys_user_id = su.id
                LEFT JOIN 
                        flame_user fu ON fpc.flame_user_id = fu.id
                WHERE 1=1
        `

        // 添加过滤条件
        args := []interface{}{}
        if info.SysUserId &gt; 0 </span><span class="cov0" title="0">{
                query += " AND fpc.sys_user_id = ?"
                args = append(args, info.SysUserId)
        }</span>
        <span class="cov0" title="0">if info.FlameUserId &gt; 0 </span><span class="cov0" title="0">{
                query += " AND fpc.flame_user_id = ?"
                args = append(args, info.FlameUserId)
        }</span>
        <span class="cov0" title="0">if info.Status &gt; 0 </span><span class="cov0" title="0">{
                query += " AND fpc.status = ?"
                args = append(args, info.Status)
        }</span>
        <span class="cov0" title="0">if info.FlameUserName != "" </span><span class="cov0" title="0">{
                query += " AND fu.nickname LIKE ?"
                args = append(args, "%"+info.FlameUserName+"%")
        }</span>

        // 添加排序和分页
        <span class="cov0" title="0">query += " ORDER BY fpc.id DESC LIMIT ? OFFSET ?"
        args = append(args, limit, offset)

        // 执行查询
        var result []CollectionWithNames
        err = global.GVA_DB.Raw(query, args...).Scan(&amp;result).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, 0, err
        }</span>

        // 如果使用了火苗用户名筛选，需要重新计算总数
        <span class="cov0" title="0">if info.FlameUserName != "" </span><span class="cov0" title="0">{
                countQuery := `
                        SELECT COUNT(*) 
                        FROM flame_player_collection fpc
                        LEFT JOIN flame_user fu ON fpc.flame_user_id = fu.id
                        WHERE fu.nickname LIKE ?
                `
                countArgs := []interface{}{"%" + info.FlameUserName + "%"}

                if info.SysUserId &gt; 0 </span><span class="cov0" title="0">{
                        countQuery += " AND fpc.sys_user_id = ?"
                        countArgs = append(countArgs, info.SysUserId)
                }</span>
                <span class="cov0" title="0">if info.FlameUserId &gt; 0 </span><span class="cov0" title="0">{
                        countQuery += " AND fpc.flame_user_id = ?"
                        countArgs = append(countArgs, info.FlameUserId)
                }</span>
                <span class="cov0" title="0">if info.Status &gt; 0 </span><span class="cov0" title="0">{
                        countQuery += " AND fpc.status = ?"
                        countArgs = append(countArgs, info.Status)
                }</span>

                <span class="cov0" title="0">global.GVA_DB.Raw(countQuery, countArgs...).Scan(&amp;count)</span>
        }

        <span class="cov0" title="0">return result, count, nil</span>
}

// GetCollectionById 根据ID获取采集记录
func (s *FlamePlayerCollectionService) GetCollectionById(id uint) (douyin.FlamePlayerCollection, error) <span class="cov0" title="0">{
        var collection douyin.FlamePlayerCollection
        err := global.GVA_DB.Where("id = ?", id).First(&amp;collection).Error
        return collection, err
}</span>

// DeleteCollection 删除采集记录
func (s *FlamePlayerCollectionService) DeleteCollection(id uint) error <span class="cov0" title="0">{
        return global.GVA_DB.Delete(&amp;douyin.FlamePlayerCollection{}, id).Error
}</span>
</pre>
		
		<pre class="file" id="file16" style="display: none">package douyin

import (
        "context"
        "encoding/json"
        "fmt"
        "time"

        "github.com/flipped-aurora/gin-vue-admin/server/global"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/response"
        "github.com/flipped-aurora/gin-vue-admin/server/model/system"
        "gorm.io/gorm"
)

type FlameUserService struct{}

var FlameUserServiceApp = new(FlameUserService)

func (s *FlameUserService) AddUser(flameUser *douyin.FlameUser) error <span class="cov0" title="0">{
        var existingUser douyin.FlameUser
        err := global.GVA_DB.Unscoped().Where("uid = ?", flameUser.UID).First(&amp;existingUser).Error
        if err == nil </span><span class="cov0" title="0">{
                // 用户已存在(包括已删除的)
                if existingUser.DeletedAt.Valid </span><span class="cov0" title="0">{
                        // 如果是已删除的记录，恢复并更新
                        return global.GVA_DB.Unscoped().Model(&amp;existingUser).Updates(map[string]interface{}{
                                "deleted_at":         nil,
                                "im_token":           flameUser.ImToken,
                                "category_id":        flameUser.CategoryId,
                                "nickname":           flameUser.Nickname,
                                "avatar":             flameUser.Avatar,
                                "unique_id":          flameUser.UniqueId,
                                "short_id":           flameUser.ShortId,
                                "sec_uid":            flameUser.SecUid,
                                "follower_count":     flameUser.FollowerCount,
                                "total_favorited":    flameUser.TotalFavorited,
                                "account_region":     flameUser.AccountRegion,
                                "province":           flameUser.Province,
                                "city":               flameUser.City,
                                "college_name":       flameUser.CollegeName,
                                "bind_phone":         flameUser.BindPhone,
                                "birthday":           flameUser.Birthday,
                                "gender":             flameUser.Gender,
                                "signature":          flameUser.Signature,
                                "sys_user_id":        flameUser.SysUserId,
                                "is_collect_enabled": flameUser.IsCollectEnabled,
                        }).Error
                }</span>
                // 如果是未删除的记录，直接更新
                <span class="cov0" title="0">return global.GVA_DB.Model(&amp;existingUser).Updates(flameUser).Error</span>
        }
        // 用户不存在,创建新用户
        <span class="cov0" title="0">return global.GVA_DB.Create(flameUser).Error</span>
}

func (s *FlameUserService) GetUserList(info request.FlameUserSearch, sysUserIds []uint) (list []douyin.FlameUser, total int64, err error) <span class="cov0" title="0">{
        limit := info.PageSize
        offset := info.PageSize * (info.Page - 1)
        db := global.GVA_DB.Model(&amp;douyin.FlameUser{})
        var flameUsers []douyin.FlameUser

        if info.Nickname != "" </span><span class="cov0" title="0">{
                db = db.Where("nickname LIKE ?", "%"+info.Nickname+"%")
        }</span>
        <span class="cov0" title="0">if info.UniqueId != "" </span><span class="cov0" title="0">{
                db = db.Where("unique_id LIKE ?", "%"+info.UniqueId+"%")
        }</span>
        <span class="cov0" title="0">if info.CategoryId &gt; 0 </span><span class="cov0" title="0">{
                db = db.Where("category_id = ?", info.CategoryId)
        }</span>
        <span class="cov0" title="0">if len(sysUserIds) &gt; 0 </span><span class="cov0" title="0">{
                db = db.Where("sys_user_id IN ?", sysUserIds)
        }</span>

        <span class="cov0" title="0">err = db.Count(&amp;total).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, 0, err
        }</span>

        <span class="cov0" title="0">err = db.Limit(limit).Offset(offset).Order("id desc").Find(&amp;flameUsers).Error

        return flameUsers, total, err</span>
}

func (s *FlameUserService) DeleteUser(id uint) error <span class="cov0" title="0">{
        return global.GVA_DB.Delete(&amp;douyin.FlameUser{}, id).Error
}</span>

func (s *FlameUserService) GetUserByToken(token string) (*douyin.FlameUser, error) <span class="cov0" title="0">{
        var user douyin.FlameUser
        err := global.GVA_DB.Where("im_token = ?", token).First(&amp;user).Error
        return &amp;user, err
}</span>

func (s *FlameUserService) ToggleCollectEnabled(id uint, enabled bool) error <span class="cov0" title="0">{
        return global.GVA_DB.Model(&amp;douyin.FlameUser{}).Where("id = ?", id).Update("is_collect_enabled", enabled).Error
}</span>

func (s *FlameUserService) UpdateUserIP(id uint, bindIP string) error <span class="cov0" title="0">{
        // 开启事务
        return global.GVA_DB.Transaction(func(tx *gorm.DB) error </span><span class="cov0" title="0">{
                // 获取用户当前绑定的IP
                var user douyin.FlameUser
                if err := tx.Where("id = ?", id).First(&amp;user).Error; err != nil </span><span class="cov0" title="0">{
                        return err
                }</span>

                // 更新用户绑定的IP
                <span class="cov0" title="0">if err := tx.Model(&amp;douyin.FlameUser{}).Where("id = ?", id).Update("bind_ip", bindIP).Error; err != nil </span><span class="cov0" title="0">{
                        return err
                }</span>

                // 如果原来有绑定IP，更新原IP的用户计数
                <span class="cov0" title="0">if user.BindIP != "" </span><span class="cov0" title="0">{
                        var oldIP douyin.IpPool
                        if err := tx.Where("ip = ?", user.BindIP).First(&amp;oldIP).Error; err == nil </span><span class="cov0" title="0">{
                                // 更新原IP的用户计数
                                dyIPService := &amp;DyIPService{}
                                if err := dyIPService.UpdateUserCount(oldIP.ID); err != nil </span><span class="cov0" title="0">{
                                        return err
                                }</span>
                        }
                }

                // 如果新绑定了IP，更新新IP的用户计数
                <span class="cov0" title="0">if bindIP != "" </span><span class="cov0" title="0">{
                        var newIP douyin.IpPool
                        if err := tx.Where("ip = ?", bindIP).First(&amp;newIP).Error; err == nil </span><span class="cov0" title="0">{
                                // 更新新IP的用户计数
                                dyIPService := &amp;DyIPService{}
                                if err := dyIPService.UpdateUserCount(newIP.ID); err != nil </span><span class="cov0" title="0">{
                                        return err
                                }</span>
                        }
                }

                <span class="cov0" title="0">return nil</span>
        })
}

// UpdateDid 更新用户的设备ID
func (s *FlameUserService) UpdateDid(id uint, did string) error <span class="cov0" title="0">{
        return global.GVA_DB.Model(&amp;douyin.FlameUser{}).Where("id = ?", id).Update("did", did).Error
}</span>

func (s *FlameUserService) BindDevice(userID uint, deviceInfo string) error <span class="cov0" title="0">{
        // 开启事务
        tx := global.GVA_DB.Begin()

        // 更新用户的设备信息
        if err := tx.Model(&amp;douyin.FlameUser{}).
                Where("id = ?", userID).
                Update("bind_device", deviceInfo).Error; err != nil </span><span class="cov0" title="0">{
                tx.Rollback()
                return err
        }</span>

        // 解析设备信息
        <span class="cov0" title="0">var deviceInfoMap map[string]interface{}
        if err := json.Unmarshal([]byte(deviceInfo), &amp;deviceInfoMap); err != nil </span><span class="cov0" title="0">{
                tx.Rollback()
                return err
        }</span>

        // 更新设备表中的用户ID
        <span class="cov0" title="0">if deviceID, ok := deviceInfoMap["id"].(float64); ok </span><span class="cov0" title="0">{
                if err := tx.Model(&amp;douyin.DeviceInfo{}).
                        Where("id = ?", uint(deviceID)).
                        Update("flame_user_id", userID).Error; err != nil </span><span class="cov0" title="0">{
                        tx.Rollback()
                        return err
                }</span>
        }

        // 提交事务
        <span class="cov0" title="0">return tx.Commit().Error</span>
}

func (s *FlameUserService) GrabFlameUsers(ctx context.Context, userID uint, collectType int) error <span class="cov0" title="0">{
        // 获取用户信息
        var user douyin.FlameUser
        if err := global.GVA_DB.Where("id = ?", userID).First(&amp;user).Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("获取用户信息失败: %v", err)
        }</span>

        // 检查用户是否开启了采集
        <span class="cov0" title="0">if !user.IsCollectEnabled </span><span class="cov0" title="0">{
                return fmt.Errorf("用户未开启采集功能")
        }</span>

        // 检查用户是否有 ImToken
        <span class="cov0" title="0">if user.ImToken == "" </span><span class="cov0" title="0">{
                return fmt.Errorf("用户未配置ImToken")
        }</span>

        // 创建采集记录
        <span class="cov0" title="0">collection := douyin.FlamePlayerCollection{
                SysUserId:   uint(user.SysUserId),
                FlameUserId: userID,
                Status:      douyin.FlamePlayerCollectionStatusProcessing, // 进行中
                Remark:      fmt.Sprintf("用户 %s 的火苗玩家采集任务", user.Nickname),
                CollectType: collectType, // 采集类型
        }
        if err := FlamePlayerCollectionServiceApp.CreateCollection(&amp;collection); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("创建采集记录失败: %v", err)
        }</span>

        // 创建定时器，每5秒执行一次
        <span class="cov0" title="0">ticker := time.NewTicker(5 * time.Second)
        defer ticker.Stop()

        // 初始化采集计数
        playerCount := 0
        // 存储已处理的玩家UID，用于去重
        processedUIDs := make(map[string]bool)
        // 当前批次待处理的用户
        currentBatch := []string{user.UID}
        // 下一批次待处理的用户
        nextBatch := make([]string, 0)

        global.GVA_LOG.Info(fmt.Sprintf("开始为用户 %s (ID: %d) 采集火苗玩家数据", user.Nickname, userID))

        // 确保在函数结束时更新采集记录状态
        defer func() </span><span class="cov0" title="0">{
                status := douyin.FlamePlayerCollectionStatusCompleted // 默认为已完成
                if ctx.Err() != nil </span><span class="cov0" title="0">{
                        status = douyin.FlamePlayerCollectionStatusInterrupted // 如果是因为上下文取消而结束，则标记为已中断
                }</span>
                <span class="cov0" title="0">FlamePlayerCollectionServiceApp.UpdateCollection(collection.ID, map[string]interface{}{
                        "status":       status,
                        "player_count": playerCount,
                })</span>
        }()

        // 批次计数
        <span class="cov0" title="0">batchCount := 1

        // 广度优先遍历
        for len(currentBatch) &gt; 0 </span><span class="cov0" title="0">{
                global.GVA_LOG.Info(fmt.Sprintf("开始处理第 %d 批次，共 %d 个用户", batchCount, len(currentBatch)))

                // 处理当前批次的每个用户
                for _, currentUID := range currentBatch </span><span class="cov0" title="0">{
                        // 检查上下文是否已取消
                        select </span>{
                        case &lt;-ctx.Done():<span class="cov0" title="0">
                                global.GVA_LOG.Info(fmt.Sprintf("用户 %s (ID: %d) 的火苗玩家采集任务已停止", user.Nickname, userID))
                                return nil</span>
                        default:<span class="cov0" title="0"></span>
                                // 继续执行
                        }

                        // 如果已经处理过，则跳过
                        <span class="cov0" title="0">if processedUIDs[currentUID] </span><span class="cov0" title="0">{
                                continue</span>
                        }

                        <span class="cov0" title="0">global.GVA_LOG.Info(fmt.Sprintf("开始采集用户 %s 的火苗玩家数据", currentUID))

                        // 标记为已处理
                        processedUIDs[currentUID] = true

                        // 采集该用户的所有火苗玩家数据（分页处理）
                        offset := 0
                        for </span><span class="cov0" title="0">{
                                // 等待定时器
                                &lt;-ticker.C

                                // 构建请求
                                req := request.CollectFlamePlayerRequest{
                                        Token:  user.ImToken,
                                        UserID: currentUID,
                                        Offset: offset,
                                }

                                // 调用 FlameApiService.CollectFlamePlayer 方法
                                resp, continueCollect, err := FlameApiServiceApp.CollectFlamePlayer(req)
                                if err != nil </span><span class="cov0" title="0">{
                                        global.GVA_LOG.Error(fmt.Sprintf("采集火苗玩家数据失败: %v, 请求参数: %+v", err, req))
                                        if continueCollect </span><span class="cov0" title="0">{
                                                offset = resp.Extra.NextOffset
                                                continue</span>
                                        }
                                        <span class="cov0" title="0">break</span> // 出错但不中断整个任务，继续处理下一个用户
                                }

                                // 处理响应
                                <span class="cov0" title="0">if resp.StatusCode != 0 </span><span class="cov0" title="0">{
                                        global.GVA_LOG.Error(fmt.Sprintf("采集火苗玩家数据失败，状态码: %d", resp.StatusCode))
                                        break</span> // 出错但不中断整个任务，继续处理下一个用户
                                }

                                // 更新偏移量
                                <span class="cov0" title="0">offset = resp.Extra.NextOffset

                                // 处理采集到的用户数据
                                for _, item := range resp.Data </span><span class="cov0" title="0">{
                                        // 将采集到的火苗玩家转换为 FlamePlayerData
                                        playerData := response.FlamePlayerData{
                                                UID:                 fmt.Sprintf("%d", item.User.Id),
                                                Nickname:            item.User.Nickname,
                                                ShortId:             fmt.Sprintf("%d", item.User.ShortId),
                                                Gender:              item.User.Gender,
                                                City:                item.User.City,
                                                BirthdayDescription: item.User.BirthdayDescription,
                                                IsFollowing:         item.User.IsFollowing,
                                                IsFollower:          item.User.IsFollower,
                                                Level:               item.User.Level,
                                                FollowerCount:       int64(item.User.Stats.FollowerCount),
                                                FollowingCount:      int64(item.User.Stats.FollowingCount),
                                                FavoriteItemCount:   int64(item.User.Stats.FavoriteItemCount),
                                                ItemCount:           int64(item.User.Stats.ItemCount),
                                                Rank:                item.Rank,
                                                Flame:               int64(item.Flame),
                                        }

                                        // 使用 FlamePlayerService 保存玩家数据
                                        if err := FlamePlayerServiceApp.SaveFlamePlayer(playerData); err != nil </span><span class="cov0" title="0">{
                                                global.GVA_LOG.Error(fmt.Sprintf("保存火苗玩家数据失败: %v", err))
                                        }</span> else<span class="cov0" title="0"> {
                                                global.GVA_LOG.Info(fmt.Sprintf("保存/更新火苗玩家: %s, ID: %s, 排名: %d, 火苗数量: %d",
                                                        playerData.Nickname, playerData.UID, playerData.Rank, playerData.Flame))
                                                playerCount++

                                                // 如果是新用户且不是当前正在处理的用户，添加到下一批次
                                                if !processedUIDs[playerData.UID] &amp;&amp; playerData.UID != currentUID &amp;&amp; playerData.UID != "" &amp;&amp; playerData.UID != "0" </span><span class="cov0" title="0">{
                                                        nextBatch = append(nextBatch, playerData.UID)
                                                }</span>
                                        }
                                }

                                // 更新采集记录中的玩家数量
                                <span class="cov0" title="0">FlamePlayerCollectionServiceApp.UpdateCollection(collection.ID, map[string]interface{}{
                                        "player_count": playerCount,
                                })

                                // 如果没有更多数据，结束当前用户的采集
                                if len(resp.Data) == 0 || offset == 0 </span><span class="cov0" title="0">{
                                        break</span>
                                }
                        }

                        <span class="cov0" title="0">global.GVA_LOG.Info(fmt.Sprintf("完成用户 %s 的火苗玩家采集", currentUID))</span>
                }

                // 当前批次处理完毕，准备处理下一批次
                <span class="cov0" title="0">global.GVA_LOG.Info(fmt.Sprintf("第 %d 批次处理完毕，共采集 %d 条数据，下一批次有 %d 个用户",
                        batchCount, playerCount, len(nextBatch)))

                // 如果没有新的用户，结束任务
                if len(nextBatch) == 0 </span><span class="cov0" title="0">{
                        global.GVA_LOG.Info(fmt.Sprintf("没有更多新用户，采集任务结束，总共采集 %d 条数据", playerCount))
                        break</span>
                }

                // 更新批次
                <span class="cov0" title="0">currentBatch = nextBatch
                nextBatch = make([]string, 0)
                batchCount++</span>
        }

        <span class="cov0" title="0">return nil</span>
}

// GetGroupedUserList 获取分组的火苗用户列表
func (s *FlameUserService) GetGroupedUserList(userId uint) ([]response.FlameUserItem, error) <span class="cov0" title="0">{
        var result []response.FlameUserItem

        // 1. 获取当前用户信息
        var currentUser system.SysUser
        if err := global.GVA_DB.First(&amp;currentUser, userId).Error; err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        // 2. 获取当前用户及其下级用户的ID列表
        <span class="cov0" title="0">var subordinateUserIds []uint
        err := global.GVA_DB.Model(&amp;system.SysUser{}).Where("created_by = ?", userId).Pluck("id", &amp;subordinateUserIds).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("获取下级用户失败: %v", err)
        }</span>

        // 递归获取更深层级的下级用户
        <span class="cov0" title="0">for i := 0; i &lt; len(subordinateUserIds); i++ </span><span class="cov0" title="0">{
                var subUserIds []uint
                if err := global.GVA_DB.Model(&amp;system.SysUser{}).Where("created_by = ?", subordinateUserIds[i]).Pluck("id", &amp;subUserIds).Error; err != nil </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("获取下级用户失败: %v", err)
                }</span>
                <span class="cov0" title="0">subordinateUserIds = append(subordinateUserIds, subUserIds...)</span>
        }

        // 3. 将当前用户添加到结果中
        <span class="cov0" title="0">currentUserItem := response.FlameUserItem{
                SysNickname: currentUser.NickName,
                FlameUsers:  []response.FlameUser{},
        }

        // 4. 获取当前用户关联的火苗用户
        var currentFlameUsers []douyin.FlameUser
        if err := global.GVA_DB.Where("sys_user_id = ?", userId).Find(&amp;currentFlameUsers).Error; err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        // 5. 将当前用户的火苗用户添加到结果中
        <span class="cov0" title="0">for _, flameUser := range currentFlameUsers </span><span class="cov0" title="0">{
                currentUserItem.FlameUsers = append(currentUserItem.FlameUsers, response.FlameUser{
                        ID:           flameUser.ID,
                        Nickname:     flameUser.Nickname,
                        ShortId:      flameUser.ShortId,
                        UniqueId:     flameUser.UniqueId,
                        ImToken:      flameUser.ImToken,
                        FlameBalance: flameUser.Flame,
                })
        }</span>

        // 如果当前用户有火苗用户，添加到结果中
        <span class="cov0" title="0">if len(currentUserItem.FlameUsers) &gt; 0 </span><span class="cov0" title="0">{
                result = append(result, currentUserItem)
        }</span>

        // 6. 处理下级用户
        <span class="cov0" title="0">if len(subordinateUserIds) &gt; 0 </span><span class="cov0" title="0">{
                // 获取所有下级用户信息
                var subordinateUsers []system.SysUser
                if err := global.GVA_DB.Where("id IN ?", subordinateUserIds).Find(&amp;subordinateUsers).Error; err != nil </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("获取下级用户信息失败: %v", err)
                }</span>

                // 处理每个下级用户
                <span class="cov0" title="0">for _, subUser := range subordinateUsers </span><span class="cov0" title="0">{
                        subUserItem := response.FlameUserItem{
                                SysNickname: subUser.NickName,
                                FlameUsers:  []response.FlameUser{},
                        }

                        // 获取下级用户关联的火苗用户
                        var subFlameUsers []douyin.FlameUser
                        if err := global.GVA_DB.Where("sys_user_id = ?", subUser.ID).Find(&amp;subFlameUsers).Error; err != nil </span><span class="cov0" title="0">{
                                return nil, fmt.Errorf("获取下级用户火苗用户失败: %v", err)
                        }</span>

                        // 将下级用户的火苗用户添加到结果中
                        <span class="cov0" title="0">for _, flameUser := range subFlameUsers </span><span class="cov0" title="0">{
                                subUserItem.FlameUsers = append(subUserItem.FlameUsers, response.FlameUser{
                                        ID:           flameUser.ID,
                                        Nickname:     flameUser.Nickname,
                                        ShortId:      flameUser.ShortId,
                                        UniqueId:     flameUser.UniqueId,
                                        ImToken:      flameUser.ImToken,
                                        FlameBalance: flameUser.Flame,
                                })
                        }</span>

                        // 如果下级用户有火苗用户，添加到结果中
                        <span class="cov0" title="0">if len(subUserItem.FlameUsers) &gt; 0 </span><span class="cov0" title="0">{
                                result = append(result, subUserItem)
                        }</span>
                }
        }

        <span class="cov0" title="0">return result, nil</span>
}

func (s *FlameUserService) GetUserByID(id uint) (*douyin.FlameUser, error) <span class="cov0" title="0">{
        var user douyin.FlameUser
        err := global.GVA_DB.Where("id = ?", id).First(&amp;user).Error
        return &amp;user, err
}</span>

func (s *FlameUserService) UpdateFlameBalance(id uint, flameBalance int64) error <span class="cov0" title="0">{
        return global.GVA_DB.Model(&amp;douyin.FlameUser{}).Where("id = ?", id).Update("flame", flameBalance).Error
}</span>
</pre>
		
		<pre class="file" id="file17" style="display: none">package douyin

import (
        "strings"
        "time"

        "github.com/flipped-aurora/gin-vue-admin/server/global"
        "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
        "github.com/flipped-aurora/gin-vue-admin/server/service/proxy"
)

type DyIPService struct{}

// IPWithCount 表示IP及其绑定的用户数
type IPWithCount struct {
        IP        string
        UserCount int
        Sort      int
}

// UserGroup 表示共用同一个IP的用户组
type UserGroup struct {
        DyUsers    []douyin.DyUser
        FlameUsers []douyin.FlameUser
}

// SyncIPs 同步IP列表
func (s *DyIPService) SyncIPs() error <span class="cov0" title="0">{
        proxyService := &amp;proxy.ExclusiveProxyService{}
        // 获取独享IP池
        resp, err := proxyService.GetExclusiveProxy()
        if err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        // 同步IP池状态（已包含用户IP绑定关系的迁移）
        <span class="cov0" title="0">if err := s.syncIPPoolStatus(resp.Data.ProxyList); err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        // 解绑已失效的IP（处理状态为false的IP的用户绑定）
        <span class="cov0" title="0">if err := s.unbindInvalidIPs(); err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        // 获取需要重新绑定IP的用户（主要处理新用户或被解绑的用户）
        <span class="cov0" title="0">dyUsersToRebind, flameUsersToRebind, err := s.getUsersToRebind()
        if err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        // 如果有需要重新绑定的用户，进行IP分配
        <span class="cov0" title="0">if len(dyUsersToRebind) &gt; 0 || len(flameUsersToRebind) &gt; 0 </span><span class="cov0" title="0">{
                // 获取可用的IP列表
                enabledIPs, err := s.getEnabledIPs()
                if err != nil </span><span class="cov0" title="0">{
                        return err
                }</span>

                // 重新分配IP
                <span class="cov0" title="0">if err := s.assignIPToRemainingUsers(dyUsersToRebind, flameUsersToRebind, &amp;enabledIPs); err != nil </span><span class="cov0" title="0">{
                        return err
                }</span>
        }

        // 更新所有IP的用户计数
        <span class="cov0" title="0">return s.updateAllIPUserCounts()</span>
}

// syncIPPoolStatus 同步IP池状态
func (s *DyIPService) syncIPPoolStatus(newProxyList []string) error <span class="cov0" title="0">{
        var listWithCity []douyin.IpWithCity
        for i, proxyStr := range newProxyList </span><span class="cov0" title="0">{
                var ip, city string
                ipWithCity := strings.Split(proxyStr, ",")
                if len(ipWithCity) &gt; 1 </span><span class="cov0" title="0">{
                        city = ipWithCity[1]
                        ip = ipWithCity[0]
                }</span> else<span class="cov0" title="0"> {
                        ip = proxyStr
                }</span>
                <span class="cov0" title="0">listWithCity = append(listWithCity, douyin.IpWithCity{
                        IP:   ip,
                        City: city,
                        Sort: i + 1,
                })</span>
        }

        // 获取现有的IP记录
        <span class="cov0" title="0">var existingIPs []douyin.IpPool
        if err := global.GVA_DB.Find(&amp;existingIPs).Error; err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        // 创建sort值到新IP的映射
        <span class="cov0" title="0">newIPBySortMap := make(map[int]douyin.IpWithCity)
        for _, item := range listWithCity </span><span class="cov0" title="0">{
                newIPBySortMap[item.Sort] = item
        }</span>

        // 创建sort值到现有IP记录的映射
        <span class="cov0" title="0">existingIPBySortMap := make(map[int]*douyin.IpPool)
        for i := range existingIPs </span><span class="cov0" title="0">{
                existingIPBySortMap[existingIPs[i].Sort] = &amp;existingIPs[i]
        }</span>

        // 第一步：更新用户的IP绑定关系（从旧IP迁移到新IP）
        <span class="cov0" title="0">for _, item := range listWithCity </span><span class="cov0" title="0">{
                if existingIP, exists := existingIPBySortMap[item.Sort]; exists </span><span class="cov0" title="0">{
                        oldIP := existingIP.IP
                        newIP := item.IP

                        // 如果IP发生了变化，需要更新用户绑定关系
                        if oldIP != newIP </span><span class="cov0" title="0">{
                                // 更新抖音用户的IP绑定
                                err := global.GVA_DB.Model(&amp;douyin.DyUser{}).
                                        Where("bind_ip = ?", oldIP).
                                        Update("bind_ip", newIP).Error
                                if err != nil </span><span class="cov0" title="0">{
                                        return err
                                }</span>

                                // 更新火山版用户的IP绑定
                                <span class="cov0" title="0">err = global.GVA_DB.Model(&amp;douyin.FlameUser{}).
                                        Where("bind_ip = ?", oldIP).
                                        Update("bind_ip", newIP).Error
                                if err != nil </span><span class="cov0" title="0">{
                                        return err
                                }</span>
                        }
                }
        }

        // 第二步：处理IP池记录
        <span class="cov0" title="0">for _, item := range listWithCity </span><span class="cov0" title="0">{
                status := true
                if existingIP, exists := existingIPBySortMap[item.Sort]; exists </span><span class="cov0" title="0">{
                        oldIP := existingIP.IP
                        newIP := item.IP

                        if oldIP != newIP </span><span class="cov0" title="0">{
                                // IP发生变化，需要删除旧记录，创建新记录，保留mac_addresses和remark
                                now := time.Now()
                                newIPRecord := douyin.IpPool{
                                        IP:             newIP,
                                        City:           item.City,
                                        Status:         &amp;status,
                                        UserCount:      existingIP.UserCount,
                                        Sort:           item.Sort,
                                        MacAddresses:   existingIP.MacAddresses, // 保留MAC地址
                                        Remark:         existingIP.Remark,       // 保留备注
                                        LastChangeTime: &amp;now,                    // 记录更换时间
                                }

                                // 先彻底删除相同IP的旧记录（包括软删除的记录）
                                if err := global.GVA_DB.Unscoped().Where("ip = ?", newIP).Delete(&amp;douyin.IpPool{}).Error; err != nil </span><span class="cov0" title="0">{
                                        return err
                                }</span>

                                // 再创建新记录
                                <span class="cov0" title="0">if err := global.GVA_DB.Create(&amp;newIPRecord).Error; err != nil </span><span class="cov0" title="0">{
                                        return err
                                }</span>

                                // 最后删除旧记录
                                <span class="cov0" title="0">if err := global.GVA_DB.Unscoped().Delete(existingIP).Error; err != nil </span><span class="cov0" title="0">{
                                        return err
                                }</span>
                        } else<span class="cov0" title="0"> {
                                // IP没有变化，只更新城市和状态
                                err := global.GVA_DB.Model(&amp;douyin.IpPool{}).Where("id = ?", existingIP.ID).Updates(map[string]interface{}{
                                        "city":   item.City,
                                        "status": status,
                                }).Error
                                if err != nil </span><span class="cov0" title="0">{
                                        return err
                                }</span>
                        }
                } else<span class="cov0" title="0"> {
                        // 如果是新的sort值，创建新记录
                        now := time.Now()
                        newIP := douyin.IpPool{
                                IP:             item.IP,
                                City:           item.City,
                                Status:         &amp;status,
                                UserCount:      0,
                                Sort:           item.Sort,
                                LastChangeTime: &amp;now, // 新IP也记录创建时间
                        }
                        if err := global.GVA_DB.Create(&amp;newIP).Error; err != nil </span><span class="cov0" title="0">{
                                return err
                        }</span>
                }
        }

        // 第三步：将不在新IP列表中的sort值对应的记录状态设置为false
        <span class="cov0" title="0">var newSorts []int
        for _, item := range listWithCity </span><span class="cov0" title="0">{
                newSorts = append(newSorts, item.Sort)
        }</span>

        <span class="cov0" title="0">if len(newSorts) &gt; 0 </span><span class="cov0" title="0">{
                if err := global.GVA_DB.Model(&amp;douyin.IpPool{}).
                        Where("sort NOT IN ?", newSorts).
                        Updates(map[string]interface{}{
                                "status":     false,
                                "user_count": 0,
                        }).Error; err != nil </span><span class="cov0" title="0">{
                        return err
                }</span>
        } else<span class="cov0" title="0"> {
                // 如果新IP列表为空，则将所有IP状态设置为false
                if err := global.GVA_DB.Model(&amp;douyin.IpPool{}).
                        Updates(map[string]interface{}{
                                "status":     false,
                                "user_count": 0,
                        }).Error; err != nil </span><span class="cov0" title="0">{
                        return err
                }</span>
        }

        <span class="cov0" title="0">return nil</span>
}

// unbindInvalidIPs 解绑已失效的IP
func (s *DyIPService) unbindInvalidIPs() error <span class="cov0" title="0">{
        // 批量解绑无效IP的用户（直接通过SQL子查询进行批量更新）
        // 解绑抖音用户的无效IP
        if err := global.GVA_DB.Model(&amp;douyin.DyUser{}).
                Where("bind_ip IS NOT NULL AND bind_ip NOT IN (SELECT ip FROM ip_pool WHERE status = true)").
                Update("bind_ip", nil).Error; err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        // 解绑火山版用户的无效IP
        <span class="cov0" title="0">if err := global.GVA_DB.Model(&amp;douyin.FlameUser{}).
                Where("bind_ip IS NOT NULL AND bind_ip NOT IN (SELECT ip FROM ip_pool WHERE status = true)").
                Update("bind_ip", nil).Error; err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        <span class="cov0" title="0">return nil</span>
}

// getUsersToRebind 获取需要重新绑定IP的用户
func (s *DyIPService) getUsersToRebind() ([]douyin.DyUser, []douyin.FlameUser, error) <span class="cov0" title="0">{
        // 获取未绑定IP的未删除用户
        var unboundDyUsers []douyin.DyUser
        if err := global.GVA_DB.Where("bind_ip IS NULL AND deleted_at IS NULL").Find(&amp;unboundDyUsers).Error; err != nil </span><span class="cov0" title="0">{
                return nil, nil, err
        }</span>

        <span class="cov0" title="0">var unboundFlameUsers []douyin.FlameUser
        if err := global.GVA_DB.Where("bind_ip IS NULL AND deleted_at IS NULL").Find(&amp;unboundFlameUsers).Error; err != nil </span><span class="cov0" title="0">{
                return nil, nil, err
        }</span>

        <span class="cov0" title="0">return unboundDyUsers, unboundFlameUsers, nil</span>
}

// getEnabledIPs 获取可用的IP列表及其用户数
func (s *DyIPService) getEnabledIPs() ([]IPWithCount, error) <span class="cov0" title="0">{
        var enabledIPs []douyin.IpPool
        if err := global.GVA_DB.Where("status = ?", true).Order("sort ASC").Find(&amp;enabledIPs).Error; err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        <span class="cov0" title="0">var ipsWithCount []IPWithCount
        for _, ip := range enabledIPs </span><span class="cov0" title="0">{
                var dyCount int64
                err := global.GVA_DB.Model(&amp;douyin.DyUser{}).Where(
                        "bind_ip = ? AND deleted_at IS NULL", ip.IP,
                ).Count(&amp;dyCount).Error
                if err != nil </span><span class="cov0" title="0">{
                        return nil, err
                }</span>

                <span class="cov0" title="0">var flameCount int64
                err = global.GVA_DB.Model(&amp;douyin.FlameUser{}).Where(
                        "bind_ip = ? AND deleted_at IS NULL", ip.IP,
                ).Count(&amp;flameCount).Error
                if err != nil </span><span class="cov0" title="0">{
                        return nil, err
                }</span>

                <span class="cov0" title="0">totalCount := int(dyCount) + int(flameCount)
                // 已经绑定大于等于2个用户的IP不参与分配
                if totalCount &lt; douyin.AutoAssignIPMaxBindUserCount </span><span class="cov0" title="0">{
                        ipsWithCount = append(ipsWithCount, IPWithCount{
                                IP:        ip.IP,
                                UserCount: totalCount,
                                Sort:      ip.Sort,
                        })
                }</span>
        }

        <span class="cov0" title="0">return ipsWithCount, nil</span>
}

// assignIPToRemainingUsers 为未绑定IP的用户分配IP
func (s *DyIPService) assignIPToRemainingUsers(
        dyUsers []douyin.DyUser,
        flameUsers []douyin.FlameUser,
        ipsWithCount *[]IPWithCount,
) error <span class="cov0" title="0">{
        // 为抖音用户分配IP
        for _, user := range dyUsers </span><span class="cov0" title="0">{
                if len(*ipsWithCount) == 0 </span><span class="cov0" title="0">{
                        break</span>
                }

                <span class="cov0" title="0">if !user.DeletedAt.Valid &amp;&amp; user.BindIP == "" </span><span class="cov0" title="0">{
                        // 分配第一个可用的IP
                        assignedIP := (*ipsWithCount)[0].IP
                        err := global.GVA_DB.Model(&amp;douyin.DyUser{}).Where("id = ?", user.ID).Update("bind_ip", assignedIP).Error
                        if err != nil </span><span class="cov0" title="0">{
                                return err
                        }</span>

                        // 更新IP用户计数
                        <span class="cov0" title="0">(*ipsWithCount)[0].UserCount++
                        if (*ipsWithCount)[0].UserCount &gt;= douyin.AutoAssignIPMaxBindUserCount </span><span class="cov0" title="0">{
                                // 移除已满的IP
                                *ipsWithCount = (*ipsWithCount)[1:]
                        }</span>
                }
        }

        // 为火山版用户分配IP
        <span class="cov0" title="0">for _, user := range flameUsers </span><span class="cov0" title="0">{
                if len(*ipsWithCount) == 0 </span><span class="cov0" title="0">{
                        break</span>
                }

                <span class="cov0" title="0">if !user.DeletedAt.Valid &amp;&amp; user.BindIP == "" </span><span class="cov0" title="0">{
                        // 分配第一个可用的IP
                        assignedIP := (*ipsWithCount)[0].IP
                        err := global.GVA_DB.Model(&amp;douyin.FlameUser{}).Where("id = ?", user.ID).Update("bind_ip", assignedIP).Error
                        if err != nil </span><span class="cov0" title="0">{
                                return err
                        }</span>

                        // 更新IP用户计数
                        <span class="cov0" title="0">(*ipsWithCount)[0].UserCount++
                        if (*ipsWithCount)[0].UserCount &gt;= douyin.AutoAssignIPMaxBindUserCount </span><span class="cov0" title="0">{
                                // 移除已满的IP
                                *ipsWithCount = (*ipsWithCount)[1:]
                        }</span>
                }
        }

        <span class="cov0" title="0">return nil</span>
}

// updateAllIPUserCounts 更新所有IP的用户计数
func (s *DyIPService) updateAllIPUserCounts() error <span class="cov0" title="0">{
        var enabledIPs []douyin.IpPool
        if err := global.GVA_DB.Where("status = ?", true).Find(&amp;enabledIPs).Error; err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        <span class="cov0" title="0">for _, ip := range enabledIPs </span><span class="cov0" title="0">{
                var dyCount int64
                if err := global.GVA_DB.Model(&amp;douyin.DyUser{}).Where("bind_ip = ?", ip.IP).Count(&amp;dyCount).Error; err != nil </span><span class="cov0" title="0">{
                        return err
                }</span>

                <span class="cov0" title="0">var flameCount int64
                err := global.GVA_DB.Model(&amp;douyin.FlameUser{}).Where("bind_ip = ?", ip.IP).Count(&amp;flameCount).Error
                if err != nil </span><span class="cov0" title="0">{
                        return err
                }</span>

                <span class="cov0" title="0">totalCount := dyCount + flameCount
                err = global.GVA_DB.Model(&amp;douyin.IpPool{}).Where("id = ?", ip.ID).Update("user_count", totalCount).Error
                if err != nil </span><span class="cov0" title="0">{
                        return err
                }</span>
        }

        <span class="cov0" title="0">return nil</span>
}

// GetIPList 获取IP列表
func (s *DyIPService) GetIPList(info request.PageInfo, userId uint) (list interface{}, total int64, err error) <span class="cov0" title="0">{
        limit := info.PageSize
        offset := info.PageSize * (info.Page - 1)
        // 过滤禁用的IP
        db := global.GVA_DB.Model(&amp;douyin.IpPool{}).Where("status = ?", true)
        // 非系统管理员，只查询用户绑定的IP
        if userId != 1 </span><span class="cov0" title="0">{
                var ips []string
                // 获取用户绑定的IP
                var dyUserIps []string
                global.GVA_DB.Model(&amp;douyin.DyUser{}).Where("sys_user_id = ?", userId).Pluck("bind_ip", &amp;dyUserIps)
                if len(dyUserIps) &gt; 0 </span><span class="cov0" title="0">{
                        ips = append(ips, dyUserIps...)
                }</span>
                <span class="cov0" title="0">var flameUserIps []string
                global.GVA_DB.Model(&amp;douyin.IpPool{}).Where("sys_user_id = ?", userId).Pluck("bind_ip", &amp;flameUserIps)
                if len(flameUserIps) &gt; 0 </span><span class="cov0" title="0">{
                        ips = append(ips, flameUserIps...)
                }</span>
                <span class="cov0" title="0">if len(ips) &gt; 0 </span><span class="cov0" title="0">{
                        db = db.Where("ip IN ?", ips)
                }</span> else<span class="cov0" title="0"> {
                        return
                }</span>
        }
        <span class="cov0" title="0">var ipList []douyin.IpPool
        err = db.Count(&amp;total).Error
        if err != nil </span><span class="cov0" title="0">{
                return
        }</span>
        <span class="cov0" title="0">err = db.Limit(limit).Offset(offset).Order("sort ASC").Find(&amp;ipList).Error
        return ipList, total, err</span>
}

// UpdateIPStatus 更新IP状态
func (s *DyIPService) UpdateIPStatus(id uint, status bool) error <span class="cov0" title="0">{
        return global.GVA_DB.Model(&amp;douyin.IpPool{}).Where("id = ?", id).Update("status", status).Error
}</span>

// GetIPBindings 获取IP绑定的用户信息
func (s *DyIPService) GetIPBindings(ipID uint) ([]douyin.DyUser, []douyin.FlameUser, error) <span class="cov0" title="0">{
        if ipID == 0 </span><span class="cov0" title="0">{
                return []douyin.DyUser{}, []douyin.FlameUser{}, nil
        }</span>

        <span class="cov0" title="0">var ip douyin.IpPool
        if err := global.GVA_DB.Where("id = ?", ipID).First(&amp;ip).Error; err != nil </span><span class="cov0" title="0">{
                return nil, nil, err
        }</span>

        // 查询抖音用户并关联手机号信息
        <span class="cov0" title="0">var dyUsersWithPhone []struct {
                douyin.DyUser
                PhoneUserName string `json:"phoneUserName"` // 手机号实名
                PhoneOperator string `json:"phoneOperator"` // 手机号运营商
        }

        err := global.GVA_DB.Select(
                "dy_user.*, phone_balance.real_name as phone_user_name, phone_balance.operator_type as phone_operator").
                Joins("left join phone_balance on dy_user.bind_phone = phone_balance.phone_number AND phone_balance.deleted_at IS NULL").
                Where("dy_user.bind_ip = ?", ip.IP).Find(&amp;dyUsersWithPhone).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, nil, err
        }</span>

        // 转换为 DyUser 类型并设置手机号信息
        <span class="cov0" title="0">var dyUsers []douyin.DyUser
        for _, userWithPhone := range dyUsersWithPhone </span><span class="cov0" title="0">{
                user := userWithPhone.DyUser
                user.PhoneUserName = userWithPhone.PhoneUserName
                // 转换运营商类型
                var phoneOperator string
                switch userWithPhone.PhoneOperator </span>{
                case "mobile":<span class="cov0" title="0">
                        phoneOperator = "中国移动"</span>
                case "unicom":<span class="cov0" title="0">
                        phoneOperator = "中国联通"</span>
                case "telecom":<span class="cov0" title="0">
                        phoneOperator = "中国电信"</span>
                case "35internet":<span class="cov0" title="0">
                        phoneOperator = "三五互联"</span>
                default:<span class="cov0" title="0">
                        phoneOperator = "未知"</span>
                }
                <span class="cov0" title="0">user.PhoneOperator = phoneOperator
                dyUsers = append(dyUsers, user)</span>
        }

        // 查询火山版用户并关联手机号信息
        <span class="cov0" title="0">var flameUsersWithPhone []struct {
                douyin.FlameUser
                PhoneUserName string `json:"phoneUserName"` // 手机号实名
                PhoneOperator string `json:"phoneOperator"` // 手机号运营商
        }

        err = global.GVA_DB.Select(
                "flame_user.*, phone_balance.real_name as phone_user_name, phone_balance.operator_type as phone_operator").
                Joins("left join phone_balance on flame_user.bind_phone = phone_balance.phone_number AND phone_balance.deleted_at IS NULL").
                Where("flame_user.bind_ip = ?", ip.IP).Find(&amp;flameUsersWithPhone).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, nil, err
        }</span>

        // 转换为 FlameUser 类型并设置手机号信息
        <span class="cov0" title="0">var flameUsers []douyin.FlameUser
        for _, userWithPhone := range flameUsersWithPhone </span><span class="cov0" title="0">{
                user := userWithPhone.FlameUser
                user.PhoneUserName = userWithPhone.PhoneUserName
                // 转换运营商类型
                var phoneOperator string
                switch userWithPhone.PhoneOperator </span>{
                case "mobile":<span class="cov0" title="0">
                        phoneOperator = "中国移动"</span>
                case "unicom":<span class="cov0" title="0">
                        phoneOperator = "中国联通"</span>
                case "telecom":<span class="cov0" title="0">
                        phoneOperator = "中国电信"</span>
                case "35internet":<span class="cov0" title="0">
                        phoneOperator = "三五互联"</span>
                default:<span class="cov0" title="0">
                        phoneOperator = "未知"</span>
                }
                <span class="cov0" title="0">user.PhoneOperator = phoneOperator
                flameUsers = append(flameUsers, user)</span>
        }

        <span class="cov0" title="0">return dyUsers, flameUsers, nil</span>
}

// UpdateUserCount 更新IP绑定的用户数量
func (s *DyIPService) UpdateUserCount(ipID uint) error <span class="cov0" title="0">{
        if ipID == 0 </span><span class="cov0" title="0">{
                return nil
        }</span>

        <span class="cov0" title="0">var ip douyin.IpPool
        if err := global.GVA_DB.Where("id = ?", ipID).First(&amp;ip).Error; err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        <span class="cov0" title="0">var dyUserCount int64
        err := global.GVA_DB.Model(&amp;douyin.DyUser{}).Where("bind_ip = ?", ip.IP).Count(&amp;dyUserCount).Error
        if err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        <span class="cov0" title="0">var flameUserCount int64
        err = global.GVA_DB.Model(&amp;douyin.FlameUser{}).Where("bind_ip = ?", ip.IP).Count(&amp;flameUserCount).Error
        if err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        <span class="cov0" title="0">totalCount := dyUserCount + flameUserCount
        return global.GVA_DB.Model(&amp;douyin.IpPool{}).Where("id = ?", ipID).Update("user_count", totalCount).Error</span>
}

// GetAvailableIP 获取可用的IP列表
func (s *DyIPService) GetAvailableIP() ([]string, error) <span class="cov0" title="0">{
        var ips []douyin.IpPool

        // 查询状态为启用
        err := global.GVA_DB.Where("status = ?", true).Order("user_count ASC").Find(&amp;ips).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        // 如果没有可用IP，返回空列表
        <span class="cov0" title="0">if len(ips) == 0 </span><span class="cov0" title="0">{
                return []string{}, nil
        }</span>

        // 提取IP地址
        <span class="cov0" title="0">var ipAddresses []string
        for _, ip := range ips </span><span class="cov0" title="0">{
                ipAddresses = append(ipAddresses, ip.IP)
        }</span>

        <span class="cov0" title="0">return ipAddresses, nil</span>
}

// 获取未被使用的ip
func (s *DyIPService) GetUnusedIP() (string, error) <span class="cov0" title="0">{
        var ip douyin.IpPool
        err := global.GVA_DB.Where("status =? AND user_count =?", true, 0).Order("sort ASC").First(&amp;ip).Error
        if err != nil </span><span class="cov0" title="0">{
                return "", err
        }</span>
        <span class="cov0" title="0">return ip.IP, nil</span>
}

// BindMacAddresses 绑定MAC地址到IP
func (s *DyIPService) BindMacAddresses(ipID uint, macAddresses []string) error <span class="cov0" title="0">{
        // 验证IP是否存在
        var ip douyin.IpPool
        if err := global.GVA_DB.Where("id = ?", ipID).First(&amp;ip).Error; err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        // 将MAC地址列表转换为字符串存储，允许为空
        <span class="cov0" title="0">var macAddressesStr string
        if len(macAddresses) &gt; 0 </span><span class="cov0" title="0">{
                // 过滤掉空字符串
                var validMacAddresses []string
                for _, mac := range macAddresses </span><span class="cov0" title="0">{
                        if strings.TrimSpace(mac) != "" </span><span class="cov0" title="0">{
                                validMacAddresses = append(validMacAddresses, strings.TrimSpace(mac))
                        }</span>
                }
                <span class="cov0" title="0">macAddressesStr = strings.Join(validMacAddresses, "\n")</span>
        }

        // 更新IP的MAC地址字段
        <span class="cov0" title="0">return global.GVA_DB.Model(&amp;douyin.IpPool{}).Where("id = ?", ipID).Update("mac_addresses", macAddressesStr).Error</span>
}

// UpdateIPRemark 更新IP备注
func (s *DyIPService) UpdateIPRemark(ipID uint, remark string) error <span class="cov0" title="0">{
        // 验证IP是否存在
        var ip douyin.IpPool
        if err := global.GVA_DB.Where("id = ?", ipID).First(&amp;ip).Error; err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        // 更新IP的备注字段
        <span class="cov0" title="0">return global.GVA_DB.Model(&amp;douyin.IpPool{}).Where("id = ?", ipID).Update("remark", remark).Error</span>
}

// GetCopyList 获取复制列表数据
func (s *DyIPService) GetCopyList(userId uint) (string, error) <span class="cov0" title="0">{
        // 获取用户能看到的IP列表（简化版的权限检查）
        var ipList []douyin.IpPool

        // 如果userId是管理员或者特殊用户，可以看到所有IP
        // 这里简化处理，获取状态为启用的IP
        if err := global.GVA_DB.Where("status = ?", true).Order("sort ASC").Find(&amp;ipList).Error; err != nil </span><span class="cov0" title="0">{
                return "", err
        }</span>

        // 从配置中获取快代理的用户名和密码
        <span class="cov0" title="0">username := global.GVA_CONFIG.Kuaidaili.Username
        password := global.GVA_CONFIG.Kuaidaili.Password

        // 生成复制数据格式：IP|端口|账号|密码
        var copyData strings.Builder
        for _, ip := range ipList </span><span class="cov0" title="0">{
                if copyData.Len() &gt; 0 </span><span class="cov0" title="0">{
                        copyData.WriteString("\n")
                }</span>
                <span class="cov0" title="0">ipStr := strings.Split(ip.IP, ":")[0]
                port := strings.Split(ip.IP, ":")[1]
                copyData.WriteString(ipStr + "|" + port + "|" + username + "|" + password)</span>
        }

        <span class="cov0" title="0">return copyData.String(), nil</span>
}
</pre>
		
		<pre class="file" id="file18" style="display: none">package douyin

import (
        "fmt"
        "sync"

        "github.com/flipped-aurora/gin-vue-admin/server/global"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/response"
)

/**
* MoreAPI
 */
type MoreApiService struct {
        client *DyHttpClient
        once   sync.Once
}

var MoreApiServiceApp = new(MoreApiService)

func (s *MoreApiService) getClient() *DyHttpClient <span class="cov0" title="0">{
        if s.client == nil </span><span class="cov0" title="0">{
                s.once.Do(func() </span><span class="cov0" title="0">{
                        s.client = NewDyHttpClient(global.GVA_CONFIG.DouyinMoreAPI.BaseUrl)
                }</span>)
        }
        <span class="cov0" title="0">return s.client</span>
}

func (s *MoreApiService) GetUserInfoByUniqueId(uniqueId string) (userInfo response.MoreApiGetUserInfoByUniqueIdResponse, err error) <span class="cov0" title="0">{
        reqBody := map[string]any{
                "user_id": uniqueId,
        }
        err = s.getClient().DoJsonRequest("POST", "/api/douyin/user_data_by_short", reqBody, &amp;userInfo)
        if err != nil </span><span class="cov0" title="0">{
                return userInfo, err
        }</span>

        <span class="cov0" title="0">return userInfo, nil</span>
}

// 获取用户最新作品(最多40条)
func (s *MoreApiService) GetUserLatestPost(secUid string) (list response.MoreApiGetUserLatestPostResponse, err error) <span class="cov0" title="0">{
        reqBody := map[string]any{
                "sec_user_id": secUid,
                "count":       "40",
        }
        err = s.getClient().DoJsonRequest("POST", "/api/douyin/user_post_v3", reqBody, &amp;list)
        if err != nil </span><span class="cov0" title="0">{
                return list, err
        }</span>
        <span class="cov0" title="0">return list, nil</span>
}

// 获取视频详情V4
func (s *MoreApiService) GetVideoDetailV4(awemeId string) (list response.MoreApiGetVideoDetailV4Response, err error) <span class="cov0" title="0">{
        reqBody := map[string]any{
                "aweme_id": awemeId,
        }
        err = s.getClient().DoJsonRequest("POST", "/api/douyin/aweme_detail_v4", reqBody, &amp;list)
        if err != nil </span><span class="cov0" title="0">{
                return list, err
        }</span>
        <span class="cov0" title="0">return list, nil</span>
}

// 获取话题详情
func (s *MoreApiService) CahllengeDetail(keyword string) (resp response.MoreApiCahllengeDetailResponse, err error) <span class="cov0" title="0">{
        reqBody := map[string]any{
                "keyword": keyword,
        }
        err = s.getClient().DoJsonRequest("POST", "/api/douyin/challenge_detail", reqBody, &amp;resp)
        if err != nil </span><span class="cov0" title="0">{
                return resp, err
        }</span>
        <span class="cov0" title="0">if resp.Code != 200 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("获取话题详情：code: %d, message:%s", resp.Code, resp.Msg)
        }</span>

        <span class="cov0" title="0">return resp, nil</span>
}

// SearchMusic 搜索音乐
func (s *MoreApiService) SearchMusic(
        req request.MoreCreatorApiSearchMusicRequest,
) (resp response.MoreCreatorApiSearchMusicResponse, err error) <span class="cov0" title="0">{
        if req.Cookie == "" </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("cookie不能为空")
        }</span>
        <span class="cov0" title="0">formData := map[string]string{
                "keyword": req.Keyword,
                "cursor":  req.Cursor,
                "count":   req.Count,
                "cookie":  req.Cookie,
        }

        err = s.getClient().DoFormRequest("POST", "/api/douyin/search_music", formData, &amp;resp)
        if err != nil </span><span class="cov0" title="0">{
                return resp, err
        }</span>

        <span class="cov0" title="0">if resp.Code != 200 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("搜索音乐失败：code: %d, message:%s", resp.Code, resp.Msg)
        }</span>

        <span class="cov0" title="0">return resp, nil</span>
}

// SearchTopic 搜索话题
func (s *MoreApiService) SearchTopic(
        req request.MoreApiSearchTopicRequest,
) (resp response.MoreApiSearchTopicResponse, err error) <span class="cov0" title="0">{
        reqBody := map[string]string{
                "keyword":    req.Keyword,
                "count":      req.Count,
                "cursor":     req.Cursor,
                "search_id":  req.SearchId,
                "timeoutSec": "5",
        }

        err = s.getClient().DoFormRequest("POST", "/api/douyin/search_topic", reqBody, &amp;resp)
        if err != nil </span><span class="cov0" title="0">{
                return resp, err
        }</span>

        <span class="cov0" title="0">if resp.Code != 200 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("搜索话题失败：code: %d, message:%s", resp.Code, resp.Msg)
        }</span>

        <span class="cov0" title="0">return resp, nil</span>
}

// GetTopComment 获取一级评论
// 请求参数：MoreApiGetTopCommentListRequest
// 返回参数：MoreApiGetTopCommentListResponse
func (s *MoreApiService) GetTopComment(req request.MoreApiGetTopCommentListRequest) (resp response.MoreApiGetTopCommentListResponse, err error) <span class="cov0" title="0">{
        reqBody := map[string]any{}
        reqBody["aweme_id"] = req.AwemeId
        reqBody["count"] = req.Count
        if req.Cursor &gt; 0 </span><span class="cov0" title="0">{
                reqBody["cursor"] = req.Cursor
        }</span>

        <span class="cov0" title="0">err = s.getClient().DoJsonRequest("POST", "/api/douyin/video_comment", reqBody, &amp;resp)
        if err != nil </span><span class="cov0" title="0">{
                return resp, err
        }</span>
        <span class="cov0" title="0">if resp.Code != 200 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("请求获取一级评论失败：code: %d, message:%s", resp.Code, resp.Msg)
        }</span>
        <span class="cov0" title="0">if resp.Data.StatusCode != 0 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("获取一级评论失败：code: %d, message:%s", resp.Data.StatusCode, resp.Data.StatusMsg)
        }</span>
        <span class="cov0" title="0">return resp, nil</span>
}

// 获取用户主页发布
func (s *MoreApiService) GetUserHomePagePost(req request.GetUserHomePagePostReq) (list response.MoreApiGetUserHomePagePostResponse, err error) <span class="cov0" title="0">{
        reqBody := map[string]any{
                "count": req.Count,
        }
        if req.SecUserId != "" </span><span class="cov0" title="0">{
                reqBody["sec_user_id"] = req.SecUserId
        }</span> else<span class="cov0" title="0"> if req.ShareText != "" </span><span class="cov0" title="0">{
                reqBody["share_text"] = req.ShareText
        }</span>

        <span class="cov0" title="0">if req.FilterType != 0 </span><span class="cov0" title="0">{
                reqBody["filter_type"] = req.FilterType
        }</span>
        <span class="cov0" title="0">if req.MaxCursor != 0 </span><span class="cov0" title="0">{
                reqBody["max_cursor"] = req.MaxCursor
        }</span>

        <span class="cov0" title="0">err = s.getClient().DoJsonRequest("POST", "/api/douyin/user_post", reqBody, &amp;list)
        if err != nil </span><span class="cov0" title="0">{
                return list, err
        }</span>
        <span class="cov0" title="0">if list.Code != 200 </span><span class="cov0" title="0">{
                return list, fmt.Errorf("请求获取一级评论失败：code: %d, message:%s", list.Code, list.Msg)
        }</span>

        <span class="cov0" title="0">return list, nil</span>
}
</pre>
		
		<pre class="file" id="file19" style="display: none">package douyin

import (
        "fmt"
        "sync"

        "github.com/flipped-aurora/gin-vue-admin/server/global"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/response"
        "github.com/flipped-aurora/gin-vue-admin/server/utils"
)

type MoreCreatorAdvanceApi struct {
        client *DyHttpClient
        once   sync.Once
}

var MoreCreatorAdvanceApiApp = new(MoreCreatorAdvanceApi)

func (s *MoreCreatorAdvanceApi) GetClient() *DyHttpClient <span class="cov0" title="0">{
        if s.client == nil </span><span class="cov0" title="0">{
                s.once.Do(func() </span><span class="cov0" title="0">{
                        s.client = NewDyHttpClient(global.GVA_CONFIG.DouyinMoreAPI.AdvanceUrl)
                }</span>)
        }

        <span class="cov0" title="0">return s.client</span>
}

// 解析商品链接
func (s *MoreCreatorAdvanceApi) ParseGoodsLink(req request.MoreCreatorAdvanceApiGoodsParseRequest) (resp response.MoreCreatorAdvanceApiGoodsParseResponse, err error) <span class="cov0" title="0">{
        formData := map[string]string{
                "cookie":         req.Cookie, // web端的cookie
                "promotion_link": req.PromotionLink,
        }

        if req.Proxy != "" </span><span class="cov0" title="0">{
                formData["proxy"] = utils.HttpProxy(req.Proxy)
        }</span>

        <span class="cov0" title="0">err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/goods_link", formData, &amp;resp)
        if err != nil </span><span class="cov0" title="0">{
                return resp, err
        }</span>
        <span class="cov0" title="0">if resp.Code != 200 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("请求获取商品链接报错：code: %d,message:%s", resp.Code, resp.Msg)
        }</span>
        <span class="cov0" title="0">if resp.Data.Code != "" </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("获取商品链接报错：code: %s,message:%s", resp.Data.Code, resp.Data.Msg)
        }</span>
        <span class="cov0" title="0">if resp.Data.StatusCode != 0 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("获取商品链接失败：status_code: %d,status_msg:%s", resp.Data.StatusCode, resp.Data.StatusMsg)
        }</span>

        <span class="cov0" title="0">return resp, nil</span>
}

// 更新购物车挂载
func (s *MoreCreatorAdvanceApi) UpdatePromotion(req request.MoreCreatorAdvanceApiUpdatePromotionRequest) (resp response.MoreCreatorAdvanceApiUpdatePromotionResponse, err error) <span class="cov0" title="0">{
        formData := map[string]string{
                "cookie":        req.Cookie, // web端的cookie
                "promotion_id":  req.PromotionId,
                "elastic_title": req.ElasticTitle,
                "elastic_img":   req.ElasticImg,
                "product_id":    req.ProductId,
        }

        if req.Proxy != "" </span><span class="cov0" title="0">{
                formData["proxy"] = utils.HttpProxy(req.Proxy)
        }</span>

        <span class="cov0" title="0">err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/update_promotion", formData, &amp;resp)
        if err != nil </span><span class="cov0" title="0">{
                return resp, err
        }</span>
        <span class="cov0" title="0">if resp.Code != 200 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("请求获取商品挂载id报错：code: %d,message:%s", resp.Code, resp.Msg)
        }</span>
        <span class="cov0" title="0">if resp.Data.Code != "" </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("获取商品链接id报错：code: %s,message:%s", resp.Data.Code, resp.Data.Msg)
        }</span>
        <span class="cov0" title="0">if resp.Data.StatusCode != 0 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("获取商品链接id失败：status_code: %d,status_msg:%s", resp.Data.StatusCode, resp.Data.StatusMsg)
        }</span>

        <span class="cov0" title="0">return resp, nil</span>
}

// 发布推广视频
func (s *MoreCreatorAdvanceApi) CreatePromotionVod(req request.MoreCreatorAdvanceApiCreateVodRequest) (resp response.MoreCreatorCustomApiCustomCreateVodResponse, err error) <span class="cov0" title="0">{
        formData := map[string]string{}
        formData["cookie"] = req.Cookie
        formData["description"] = req.Description
        formData["visibility_type"] = req.VisibilityType
        if req.Proxy != "" </span><span class="cov0" title="0">{
                formData["proxy"] = utils.HttpProxy(req.Proxy)
        }</span>
        <span class="cov0" title="0">if req.VideoVid != "" </span><span class="cov0" title="0">{
                formData["video_vid"] = req.VideoVid
        }</span>
        <span class="cov0" title="0">if req.Download != "" </span><span class="cov0" title="0">{
                formData["download"] = req.Download
        }</span>
        <span class="cov0" title="0">if req.Challenges != "" </span><span class="cov0" title="0">{
                formData["challenges"] = req.Challenges
        }</span>
        <span class="cov0" title="0">if req.Timing != "" </span><span class="cov0" title="0">{
                formData["timing"] = req.Timing
        }</span>
        <span class="cov0" title="0">if req.PoiName != "" </span><span class="cov0" title="0">{
                formData["poi_name"] = req.PoiName
        }</span>
        <span class="cov0" title="0">if req.PoiId != "" </span><span class="cov0" title="0">{
                formData["poi_id"] = req.PoiId
        }</span>
        <span class="cov0" title="0">if req.ShopDraftId != "" </span><span class="cov0" title="0">{
                formData["shop_draft_id"] = req.ShopDraftId
        }</span>

        <span class="cov0" title="0">remoteFiles := map[string]string{}
        if req.UploadPosterPath != "" </span><span class="cov0" title="0">{
                remoteFiles["upload_poster"] = req.UploadPosterPath
        }</span>
        <span class="cov0" title="0">if req.VideoPath != "" </span><span class="cov0" title="0">{
                remoteFiles["video"] = req.VideoPath
        }</span>

        <span class="cov0" title="0">err = s.GetClient().DoMultipartRequest("POST", "/api/douyin_v1/create_vod", formData, nil, remoteFiles, &amp;resp)
        if err != nil </span><span class="cov0" title="0">{
                return resp, err
        }</span>
        <span class="cov0" title="0">if resp.Status != 0 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("发布抖音视频失败：服务器报错code: %d,message:%s", resp.Status, resp.Message)
        }</span>
        <span class="cov0" title="0">if resp.Code != 200 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("发布抖音视频失败：code: %d,message:%s", resp.Code, resp.Msg)
        }</span>

        <span class="cov0" title="0">return resp, nil</span>
}

func (s *MoreCreatorAdvanceApi) PublishPromotionVod(req request.MoreCreatorAdvanceApiCreatePromotionRequest) (resp response.MoreCreatorCustomApiCustomCreateVodResponse, err error) <span class="cov0" title="0">{
        parseReq := request.MoreCreatorAdvanceApiGoodsParseRequest{
                Cookie:        req.WebCookie,
                PromotionLink: req.PromotionLink,
                Proxy:         req.Proxy,
        }
        fmt.Printf("发布带货视频:解析带货链接:%v", parseReq)
        parseResp, err := s.ParseGoodsLink(parseReq)
        if err != nil </span><span class="cov0" title="0">{
                fmt.Printf("解析商品链接报错:%v", err)
                return resp, err
        }</span>
        // 解析商品链接
        <span class="cov0" title="0">imgStr := ""
        for _, img := range parseResp.Data.Promotions[0].Imgs </span><span class="cov0" title="0">{
                if imgStr != "" </span><span class="cov0" title="0">{
                        imgStr += img.Uri
                }</span> else<span class="cov0" title="0"> {
                        imgStr = fmt.Sprintf("%s,%s", imgStr, img.Uri)
                }</span>
        }
        // 更新购物车挂载
        <span class="cov0" title="0">updateReq := request.MoreCreatorAdvanceApiUpdatePromotionRequest{
                Cookie:       req.WebCookie,
                Proxy:        req.Proxy,
                PromotionId:  parseResp.Data.Promotions[0].PromotionId,
                ElasticTitle: req.PromotionTitle,
                ElasticImg:   imgStr,
                ProductId:    parseResp.Data.Promotions[0].Gid,
        }
        fmt.Printf("发布带货视频:更新购物车挂载:%v", updateReq)
        updateResp, err := s.UpdatePromotion(updateReq)
        if err != nil </span><span class="cov0" title="0">{
                return resp, err
        }</span>
        // 发布视频
        <span class="cov0" title="0">createVodReq := request.MoreCreatorAdvanceApiCreateVodRequest{
                Cookie:           req.Cookie,
                Proxy:            req.Proxy,
                Description:      req.Description,
                UploadPosterPath: req.UploadPosterPath,
                VisibilityType:   req.VisibilityType,
                VideoPath:        req.VideoPath,
                VideoVid:         req.VideoVid,
                Download:         req.Download,
                Challenges:       req.Challenges,
                Timing:           req.Timing,
                PoiName:          req.PoiName,
                PoiId:            req.PoiId,
                ProductUrl:       req.ProductUrl,
                ShopDraftId:      updateResp.Data.ShopDraftId,
        }
        fmt.Printf("发布带货视频:发布视频:%v", createVodReq)
        resp, err = s.CreatePromotionVod(createVodReq)
        return</span>
}
</pre>
		
		<pre class="file" id="file20" style="display: none">package douyin

import (
        "fmt"
        "sync"

        "github.com/flipped-aurora/gin-vue-admin/server/global"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/response"
        "github.com/flipped-aurora/gin-vue-admin/server/utils"
)

/**
* MoreAPI创作者中心
 */
type MoreCreatorApiService struct {
        client *DyHttpClient
        once   sync.Once
}

var MoreCreatorApiServiceApp = new(MoreCreatorApiService)

func (s *MoreCreatorApiService) GetClient() *DyHttpClient <span class="cov0" title="0">{
        if s.client == nil </span><span class="cov0" title="0">{
                s.once.Do(func() </span><span class="cov0" title="0">{
                        s.client = NewDyHttpClient(global.GVA_CONFIG.DouyinMoreAPI.CreatorUrl)
                }</span>)
        }
        <span class="cov0" title="0">return s.client</span>
}

func (s *MoreCreatorApiService) GetQRCode(req request.MoreCreatorApiGetQRCodeRequest) (resp response.MoreCreatorApiGetQRCodeResponse, err error) <span class="cov0" title="0">{
        formData := map[string]string{}
        if req.Proxy != "" </span><span class="cov0" title="0">{
                formData["proxy"] = utils.HttpProxy(req.Proxy)
        }</span>
        <span class="cov0" title="0">if req.Did != "" </span><span class="cov0" title="0">{
                formData["did"] = req.Did
        }</span>
        <span class="cov0" title="0">if req.Iid != "" </span><span class="cov0" title="0">{
                formData["iid"] = req.Iid
        }</span>
        <span class="cov0" title="0">err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/get_qrcode", formData, &amp;resp)
        if err != nil </span><span class="cov0" title="0">{
                return resp, err
        }</span>
        <span class="cov0" title="0">if resp.Code != 200 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("获取二维码GetQRCode失败：code: %d,message:%s", resp.Code, resp.Msg)
        }</span>

        <span class="cov0" title="0">return resp, nil</span>
}

func (s *MoreCreatorApiService) CheckQRCode(req request.MoreCreatorApiCheckQRCodeRequest) (resp response.MoreCreatorApiCheckQRCodeResponse, err error) <span class="cov0" title="0">{
        formData := map[string]string{
                "token": req.Token,
        }
        if req.Proxy != "" </span><span class="cov0" title="0">{
                formData["proxy"] = utils.HttpProxy(req.Proxy)
        }</span>
        <span class="cov0" title="0">err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/check_qrcode", formData, &amp;resp)
        if err != nil </span><span class="cov0" title="0">{
                return resp, err
        }</span>
        <span class="cov0" title="0">if resp.Code != 200 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("请求检查二维码接口失败：code: %d,message:%s", resp.Code, resp.Msg)
        }</span>

        <span class="cov0" title="0">return resp, nil</span>
}

// 企业号设备注册
func (s *MoreCreatorApiService) DeviceRegister() (resp response.MoreCreatorApiDeviceRegisterResponse, err error) <span class="cov0" title="0">{
        err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/device_register", nil, &amp;resp)
        if err != nil </span><span class="cov0" title="0">{
                return resp, err
        }</span>
        <span class="cov0" title="0">if resp.Code != 200 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("请求发送验证码失败：code: %d,message:%s", resp.Code, resp.Msg)
        }</span>

        <span class="cov0" title="0">return resp, nil</span>
}

func (s *MoreCreatorApiService) SendCaptcha(req request.MoreCreatorApiSendCaptchaRequest) (resp response.MoreCreatorApiCheckCaptchaResponse, err error) <span class="cov0" title="0">{
        formData := map[string]string{
                "token":       req.Token,
                "encrypt_uid": req.EncryptUid,
        }
        if req.Proxy != "" </span><span class="cov0" title="0">{
                formData["proxy"] = utils.HttpProxy(req.Proxy)
        }</span>
        <span class="cov0" title="0">if req.EncryptOperStaffUid != "" </span><span class="cov0" title="0">{
                formData["encrypt_oper_staff_uid"] = req.EncryptOperStaffUid
        }</span>

        <span class="cov0" title="0">fmt.Printf("发送短信参数：%+v", formData)
        err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/send_sms", formData, &amp;resp)
        if err != nil </span><span class="cov0" title="0">{
                return resp, err
        }</span>
        <span class="cov0" title="0">if resp.Code != 200 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("请求发送验证码失败：code: %d,message:%s", resp.Code, resp.Msg)
        }</span>

        <span class="cov0" title="0">return resp, nil</span>
}

func (s *MoreCreatorApiService) ValidCaptcha(req request.MoreCreatorApiValidCaptchaRequest) (resp response.MoreCreatorApiValidCaptchaResponse, err error) <span class="cov0" title="0">{
        formData := map[string]string{
                "token":       req.Token,
                "encrypt_uid": req.EncryptUid,
                "code":        req.Code,
        }
        if req.Proxy != "" </span><span class="cov0" title="0">{
                formData["proxy"] = utils.HttpProxy(req.Proxy)
        }</span>
        <span class="cov0" title="0">if req.EncryptOperStaffUid != "" </span><span class="cov0" title="0">{
                formData["encrypt_oper_staff_uid"] = req.EncryptOperStaffUid
        }</span>

        <span class="cov0" title="0">err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/verify_sms", formData, &amp;resp)
        if err != nil </span><span class="cov0" title="0">{
                return resp, err
        }</span>
        <span class="cov0" title="0">if resp.Code != 200 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("请求校验验证码失败：code: %d,message:%s", resp.Code, resp.Msg)
        }</span>
        <span class="cov0" title="0">if resp.Data.Data.Description != "" </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("请求校验验证码失败：description:%s", resp.Data.Data.Description)
        }</span>

        <span class="cov0" title="0">return resp, nil</span>
}

// 添加 GetLoginUserInfo 方法
func (s *MoreCreatorApiService) GetLoginUserInfo(req request.MoreCreatorApiVGetLoginUserInfoRequest) (resp response.MoreCreatorApiVGetLoginUserInfoResponse, err error) <span class="cov0" title="0">{
        formData := map[string]string{
                "cookie": req.Cookie,
        }
        if req.Proxy != "" </span><span class="cov0" title="0">{
                formData["proxy"] = utils.HttpProxy(req.Proxy)
        }</span>

        <span class="cov0" title="0">err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/creator_info", formData, &amp;resp)
        if err != nil </span><span class="cov0" title="0">{
                resp.CookieCode = 2
                return resp, fmt.Errorf("请求报错：code: %d,message:%s, err:%s", resp.Code, resp.Msg, err.Error())
        }</span>

        <span class="cov0" title="0">if resp.Code == 0 &amp;&amp; resp.Msg == "" </span><span class="cov0" title="0">{
                resp.Code = 512
                resp.Msg = "获取登录用户信息失败"
                resp.CookieCode = 2
                return resp, fmt.Errorf("请求超时：code: %d,message:%s", resp.Code, resp.Msg)
        }</span>

        <span class="cov0" title="0">if resp.Code != 200 </span><span class="cov0" title="0">{
                resp.CookieCode = 2
                return resp, fmt.Errorf("获取登录用户信息失败：code: %d,message:%s", resp.Code, resp.Msg)
        }</span>

        <span class="cov0" title="0">if resp.Data.StatusCode != 0 </span><span class="cov0" title="0">{
                resp.CookieCode = 1
                return resp, fmt.Errorf("获取登录用户信息失败：status_code: %d,status_msg:%s", resp.Data.StatusCode, resp.Data.StatusMsg)
        }</span>

        <span class="cov0" title="0">return resp, nil</span>
}

// VodUpload 异步上传发布视频
func (s *MoreCreatorApiService) VodUpload(
        req request.MoreCreatorApiVodUploadRequest,
) (resp response.MoreCreatorApiVodUploadResponse, err error) <span class="cov0" title="0">{
        formData := map[string]string{
                "cookie": req.Cookie,
        }

        remoteFiles := map[string]string{
                "video": req.VideoPath,
        }

        err = s.GetClient().DoMultipartRequest("POST", "/api/douyin_v1/vod_upload", formData, nil, remoteFiles, &amp;resp)
        if err != nil </span><span class="cov0" title="0">{
                return resp, err
        }</span>

        <span class="cov0" title="0">if resp.Code != 200 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("异步上传视频失败：code: %d, message:%s", resp.Code, resp.Msg)
        }</span>

        <span class="cov0" title="0">return resp, nil</span>
}

// GetVodUploadResult 获取异步上传视频结果
func (s *MoreCreatorApiService) GetVodUploadResult(
        req request.MoreCreatorApiGetVodUploadResultRequest,
) (resp response.MoreCreatorApiGetVodUploadResultResponse, err error) <span class="cov0" title="0">{
        formData := map[string]string{
                "task_id": req.TaskId,
        }

        err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/vod_upload_result", formData, &amp;resp)
        if err != nil </span><span class="cov0" title="0">{
                return resp, err
        }</span>

        <span class="cov0" title="0">if resp.Code != 200 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("获取上传结果失败：code: %d, message:%s", resp.Code, resp.Msg)
        }</span>

        <span class="cov0" title="0">return resp, nil</span>
}
</pre>
		
		<pre class="file" id="file21" style="display: none">package douyin

import (
        "fmt"
        "sync"

        "github.com/flipped-aurora/gin-vue-admin/server/global"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/response"
        "github.com/flipped-aurora/gin-vue-admin/server/utils"
)

/**
* MoreAPI自定义创作者中心:
 */
type MoreCreatorCustomApiService struct {
        client *DyHttpClient
        once   sync.Once
}

var MoreCreatorCustomApiServiceApp = new(MoreCreatorCustomApiService)

func (s *MoreCreatorCustomApiService) GetClient() *DyHttpClient <span class="cov0" title="0">{
        if s.client == nil </span><span class="cov0" title="0">{
                s.once.Do(func() </span><span class="cov0" title="0">{
                        s.client = NewDyHttpClient(global.GVA_CONFIG.DouyinMoreAPI.CustomUrl)
                }</span>)
        }
        <span class="cov0" title="0">return s.client</span>
}

// GetIncomeCategorySummary 获取收入分类汇总
func (s *MoreCreatorCustomApiService) GetIncomeCategorySummary(req request.MoreCreatorCustomApiIncomeCategorySummaryRequest) (resp response.MoreCreatorCustomApiIncomeCategorySummaryResponse, err error) <span class="cov0" title="0">{
        formData := map[string]string{}
        formData["cookie"] = req.Cookie
        if req.Proxy != "" </span><span class="cov0" title="0">{
                formData["proxy"] = utils.HttpProxy(req.Proxy)
        }</span>
        <span class="cov0" title="0">if req.CategoryId != "" </span><span class="cov0" title="0">{
                formData["category_id"] = req.CategoryId
        }</span>
        <span class="cov0" title="0">err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/income_category_summary", formData, &amp;resp)
        if err != nil </span><span class="cov0" title="0">{
                return resp, err
        }</span>
        <span class="cov0" title="0">if resp.Code != 200 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("获取收入分类汇总失败：code: %d,message:%s", resp.Code, resp.Msg)
        }</span>

        <span class="cov0" title="0">return resp, nil</span>
}

// PublishVideo 发布视频
func (s *MoreCreatorCustomApiService) CustomCreateVod(req request.MoreCreatorCustomApiCustomCreateVodRequest) (resp response.MoreCreatorCustomApiCustomCreateVodResponse, err error) <span class="cov0" title="0">{
        formData := map[string]string{}
        formData["cookie"] = req.Cookie
        formData["description"] = req.Description
        formData["visibility_type"] = req.VisibilityType
        if req.Proxy != "" </span><span class="cov0" title="0">{
                formData["proxy"] = utils.HttpProxy(req.Proxy)
        }</span>
        <span class="cov0" title="0">if req.VideoVid != "" </span><span class="cov0" title="0">{
                formData["video_vid"] = req.VideoVid
        }</span>
        <span class="cov0" title="0">if req.Download != "" </span><span class="cov0" title="0">{
                formData["download"] = req.Download
        }</span>
        <span class="cov0" title="0">if req.Challenges != "" </span><span class="cov0" title="0">{
                formData["challenges"] = req.Challenges
        }</span>
        <span class="cov0" title="0">if req.Timing != "" </span><span class="cov0" title="0">{
                formData["timing"] = req.Timing
        }</span>
        <span class="cov0" title="0">if req.PoiName != "" </span><span class="cov0" title="0">{
                formData["poi_name"] = req.PoiName
        }</span>
        <span class="cov0" title="0">if req.PoiId != "" </span><span class="cov0" title="0">{
                formData["poi_id"] = req.PoiId
        }</span>
        <span class="cov0" title="0">if req.MusicId != "" </span><span class="cov0" title="0">{
                formData["music_id"] = req.MusicId
        }</span>
        <span class="cov0" title="0">if req.MusicIdEndTime != "" </span><span class="cov0" title="0">{
                formData["music_end_time"] = req.MusicIdEndTime
        }</span>

        <span class="cov0" title="0">remoteFiles := map[string]string{}
        if req.UploadPosterPath != "" </span><span class="cov0" title="0">{
                remoteFiles["upload_poster"] = req.UploadPosterPath
        }</span>
        <span class="cov0" title="0">if req.VideoPath != "" </span><span class="cov0" title="0">{
                remoteFiles["video"] = req.VideoPath
        }</span>

        <span class="cov0" title="0">err = s.GetClient().DoMultipartRequest("POST", "/api/douyin_v1/create_vod", formData, nil, remoteFiles, &amp;resp)
        if err != nil </span><span class="cov0" title="0">{
                return resp, err
        }</span>
        <span class="cov0" title="0">if resp.Status != 0 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("发布抖音视频失败：服务器报错code: %d,message:%s", resp.Status, resp.Message)
        }</span>
        <span class="cov0" title="0">if resp.Code != 200 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("发布抖音视频失败：code: %d,message:%s", resp.Code, resp.Msg)
        }</span>

        <span class="cov0" title="0">return resp, nil</span>
}

// GetWorkList 获取作品列表
func (s *MoreCreatorCustomApiService) GetWorkList(req request.MoreCreatorCustomApiWorkListRequest) (resp response.MoreCreatorCustomApiWorkListResponse, err error) <span class="cov0" title="0">{
        formData := map[string]string{}
        formData["cookie"] = req.Cookie
        formData["count"] = fmt.Sprint(req.Count)
        formData["status"] = fmt.Sprint(req.Status)
        if req.Proxy != "" </span><span class="cov0" title="0">{
                formData["proxy"] = utils.HttpProxy(req.Proxy)
        }</span>
        <span class="cov0" title="0">if req.MaxCursor != "" </span><span class="cov0" title="0">{
                formData["max_cursor"] = req.MaxCursor
        }</span>

        // 发送请求
        <span class="cov0" title="0">err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/work_list", formData, &amp;resp)
        if err != nil </span><span class="cov0" title="0">{
                return resp, err
        }</span>
        <span class="cov0" title="0">if resp.Code != 200 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("请求抖音作品列表失败：code: %d,message:%s, msg: %s", resp.Code, resp.Message, resp.Msg)
        }</span>

        <span class="cov0" title="0">return resp, nil</span>
}

// 修改作品权限
func (s *MoreCreatorCustomApiService) UpdateWorkVisibility(req request.MoreCreatorCustomApiWorkModifyRequest) (resp response.MoreCreatorCustomApiCustomGenernalResponse, err error) <span class="cov0" title="0">{
        formData := map[string]string{}
        formData["cookie"] = req.Cookie
        formData["item_id"] = req.ItemId
        formData["visibility_type"] = fmt.Sprint(req.VisibilityType)
        if req.Download != 0 </span><span class="cov0" title="0">{
                formData["download"] = fmt.Sprint(req.Download)
        }</span>
        <span class="cov0" title="0">if req.Proxy != "" </span><span class="cov0" title="0">{
                formData["proxy"] = utils.HttpProxy(req.Proxy)
        }</span>

        // 发送请求
        <span class="cov0" title="0">err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/update_aweme", formData, &amp;resp)
        if err != nil </span><span class="cov0" title="0">{
                return resp, err
        }</span>
        <span class="cov0" title="0">if resp.Code != 200 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("请求修改抖音作品失败：code: %d,message:%s", resp.Code, resp.Msg)
        }</span>

        <span class="cov0" title="0">if resp.Data.StatusCode != 0 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("修改抖音作品失败：status_code: %d,status_message:%s", resp.Data.StatusCode, resp.Data.StatusMessage)
        }</span>

        <span class="cov0" title="0">return resp, nil</span>
}

// 删除作品
func (s *MoreCreatorCustomApiService) DeleteWork(req request.MoreCreatorCustomApiWorkDeleteRequest) (resp response.MoreCreatorCustomApiCustomGenernalResponse, err error) <span class="cov0" title="0">{
        formData := map[string]string{}
        formData["cookie"] = req.Cookie
        formData["item_id"] = req.ItemId
        if req.Proxy != "" </span><span class="cov0" title="0">{
                formData["proxy"] = utils.HttpProxy(req.Proxy)
        }</span>

        // 发送请求
        <span class="cov0" title="0">err = s.GetClient().DoFormRequest("POST", "/api/douyin_v1/delete_aweme", formData, &amp;resp)
        if err != nil </span><span class="cov0" title="0">{
                return resp, err
        }</span>
        <span class="cov0" title="0">if resp.Code != 200 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("请求删除抖音作品失败：code: %d,message:%s", resp.Code, resp.Msg)
        }</span>

        <span class="cov0" title="0">if resp.Data.StatusCode != 0 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("删除抖音作品失败：status_code: %d,status_message:%s", resp.Data.StatusCode, resp.Data.StatusMessage)
        }</span>

        <span class="cov0" title="0">return resp, nil</span>
}
</pre>
		
		<pre class="file" id="file22" style="display: none">package douyin

import (
        "fmt"
        "sync"

        "github.com/flipped-aurora/gin-vue-admin/server/global"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/response"
        "github.com/flipped-aurora/gin-vue-admin/server/utils"
)

// 在文件顶部添加以下初始化代码
type MoreMessageApiService struct {
        client *DyHttpClient
        once   sync.Once
}

var MoreMessageApiServiceApp = new(MoreMessageApiService)

func (s *MoreMessageApiService) getClient() *DyHttpClient <span class="cov0" title="0">{
        if s.client == nil </span><span class="cov0" title="0">{
                s.once.Do(func() </span><span class="cov0" title="0">{
                        s.client = NewDyHttpClient(global.GVA_CONFIG.DouyinMoreAPI.MessageUrl)
                }</span>)
        }
        <span class="cov0" title="0">return s.client</span>
}

// SendMessage 发送消息接口
func (s *MoreMessageApiService) Send(req request.MoreMessageApiSendRequest) (resp response.MoreMessageApiSendResponse, err error) <span class="cov0" title="0">{
        reqBody := map[string]any{
                "sec_user_id": req.SecUserID,
                "message":     req.Message,
                "cookie":      req.Cookie,
        }
        if req.Proxy != "" </span><span class="cov0" title="0">{
                reqBody["proxy"] = utils.HttpProxy(req.Proxy)
        }</span>
        <span class="cov0" title="0">err = s.getClient().DoJsonRequest("POST", "/api/douyin/send_message", reqBody, &amp;resp)
        if err != nil </span><span class="cov0" title="0">{
                return resp, err
        }</span>
        <span class="cov0" title="0">if resp.Code != 200 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("请求发送消息失败：code: %d,message:%s", resp.Code, resp.Msg)
        }</span>
        <span class="cov0" title="0">if resp.Data.StatusCode != 0 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf("发送消息失败：code: %d,message:%s", resp.Data.StatusCode, resp.Data.StatusMsg)
        }</span>
        <span class="cov0" title="0">if resp.Data.Body.SendMessageBody.CheckCode != 0 </span><span class="cov0" title="0">{
                return resp, fmt.Errorf(resp.Data.Body.SendMessageBody.CheckMessage)
        }</span>

        <span class="cov0" title="0">return</span>
}
</pre>
		
		<pre class="file" id="file23" style="display: none">package douyin

import (
        "encoding/json"
        "fmt"
        "net/http"
        "net/url"
        "strconv"
        "time"

        "github.com/flipped-aurora/gin-vue-admin/server/global"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
        "github.com/flipped-aurora/gin-vue-admin/server/model/douyin/response"
)

// PhoneBalanceService 手机话费余额查询服务
type PhoneBalanceService struct{}

// 提取的公共查询函数
func (s *PhoneBalanceService) queryPhoneBalanceFromAPI(phoneNumber string, operatorType string) (float64, error) <span class="cov0" title="0">{
        // 从配置文件中获取API密钥
        apiKey := global.GVA_CONFIG.Taolale.ApiKey
        if apiKey == "" </span><span class="cov0" title="0">{
                return 0, fmt.Errorf("未配置淘辣了API密钥")
        }</span>

        // 获取API基础URL
        <span class="cov0" title="0">apiBaseURL := global.GVA_CONFIG.Taolale.ApiURL
        if apiBaseURL == "" </span><span class="cov0" title="0">{
                apiBaseURL = "https://api.taolale.com/api"
        }</span>

        // 根据运营商类型选择不同的API接口
        <span class="cov0" title="0">var apiPath string
        switch operatorType </span>{
        case "mobile":<span class="cov0" title="0">
                apiPath = "/Inquiry_Phone_Charges/get_yd"</span>
        case "unicom":<span class="cov0" title="0">
                apiPath = "/Inquiry_Phone_Charges/get_lt"</span>
        case "telecom":<span class="cov0" title="0">
                apiPath = "/Inquiry_Phone_Charges/get_dx"</span>
        default:<span class="cov0" title="0">
                return 0, fmt.Errorf("不支持的运营商类型: %s", operatorType)</span>
        }

        // 构建请求URL
        <span class="cov0" title="0">reqURL, err := url.Parse(apiBaseURL + apiPath)
        if err != nil </span><span class="cov0" title="0">{
                return 0, fmt.Errorf("构建请求URL失败: %w", err)
        }</span>

        // 添加查询参数
        <span class="cov0" title="0">query := reqURL.Query()
        query.Add("key", apiKey)
        query.Add("mobile", phoneNumber)
        reqURL.RawQuery = query.Encode()

        // 设置HTTP客户端
        httpClient := &amp;http.Client{
                Timeout: 10 * time.Second,
        }

        // 添加重试逻辑
        const maxRetries = 3
        var httpResp *http.Response
        var lastErr error

        for retries := 0; retries &lt;= maxRetries; retries++ </span><span class="cov0" title="0">{
                if retries &gt; 0 </span><span class="cov0" title="0">{
                        global.GVA_LOG.Info(fmt.Sprintf("正在进行第 %d 次重试查询手机话费余额(%s)...", retries, phoneNumber))
                        // 重试前等待一小段时间
                        time.Sleep(time.Duration(retries) * 500 * time.Millisecond)
                }</span>

                <span class="cov0" title="0">httpResp, err = httpClient.Get(reqURL.String())
                if err == nil </span><span class="cov0" title="0">{
                        break</span> // 请求成功，跳出重试循环
                }

                <span class="cov0" title="0">lastErr = err
                global.GVA_LOG.Error(fmt.Sprintf("发送HTTP请求失败 (尝试 %d/%d): %v", retries+1, maxRetries+1, err))

                // 如果已经是最后一次重试，则返回错误
                if retries == maxRetries </span><span class="cov0" title="0">{
                        return 0, fmt.Errorf("发送HTTP请求失败，已重试%d次: %w", maxRetries, lastErr)
                }</span>
        }

        <span class="cov0" title="0">defer httpResp.Body.Close()

        // 解析响应
        var taolaleResp struct {
                Code int    `json:"code"`
                Msg  string `json:"msg"`
                Data struct {
                        Mobile    string      `json:"mobile"`
                        CurFee    interface{} `json:"curFee"`
                        MobileFee interface{} `json:"mobile_fee"`
                } `json:"data"`
        }

        if err := json.NewDecoder(httpResp.Body).Decode(&amp;taolaleResp); err != nil </span><span class="cov0" title="0">{
                return 0, fmt.Errorf("解析响应失败: %w", err)
        }</span>

        // 检查响应状态
        <span class="cov0" title="0">if taolaleResp.Code != 200 </span><span class="cov0" title="0">{
                return 0, fmt.Errorf("查询手机话费余额失败: %s", taolaleResp.Msg)
        }</span>

        // 解析余额 - 兼容不同类型
        <span class="cov0" title="0">var balance float64
        switch v := taolaleResp.Data.CurFee.(type) </span>{
        case string:<span class="cov0" title="0">
                balance, err = strconv.ParseFloat(v, 64)
                if err != nil </span><span class="cov0" title="0">{
                        return 0, fmt.Errorf("解析余额失败: %w", err)
                }</span>
        case float64:<span class="cov0" title="0">
                balance = v</span>
        default:<span class="cov0" title="0">
                return 0, fmt.Errorf("不支持的余额数据类型")</span>
        }

        <span class="cov0" title="0">return balance, nil</span>
}

// QueryPhoneBalance 查询手机话费余额
func (s *PhoneBalanceService) QueryPhoneBalance(req request.PhoneBalanceQuery, userId uint, ip string) (*response.PhoneBalanceResponse, error) <span class="cov0" title="0">{
        // 查找是否已存在该手机号的记录
        var existingRecord douyin.PhoneBalance
        result := global.GVA_DB.Where("phone_number = ?", req.PhoneNumber).First(&amp;existingRecord)

        if result.Error == nil </span><span class="cov0" title="0">{
                // 记录已存在，返回错误
                return nil, fmt.Errorf("该手机号 %s 已存在，录入者：%d", req.PhoneNumber, existingRecord.RequestUserID)
        }</span>

        // 调用公共查询函数获取余额
        <span class="cov0" title="0">balance, err := s.queryPhoneBalanceFromAPI(req.PhoneNumber, req.OperatorType)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        // 构建响应
        <span class="cov0" title="0">resp := &amp;response.PhoneBalanceResponse{
                PhoneNumber:  req.PhoneNumber,
                OperatorType: req.OperatorType,
                RealName:     req.RealName,
                Balance:      balance,
                QueryTime:    time.Now().Unix(),
                QueryStatus:  0, // 成功
                QueryMessage: "查询成功",
        }

        // 准备记录数据
        record := douyin.PhoneBalance{
                PhoneNumber:   req.PhoneNumber,
                OperatorType:  req.OperatorType,
                RealName:      req.RealName,
                Balance:       balance,
                QueryTime:     resp.QueryTime,
                QueryStatus:   resp.QueryStatus,
                QueryMessage:  resp.QueryMessage,
                RequestIP:     ip,
                RequestUserID: userId,
        }

        // 创建新记录
        if err := global.GVA_DB.Create(&amp;record).Error; err != nil </span><span class="cov0" title="0">{
                global.GVA_LOG.Error(fmt.Sprintf("保存手机话费余额查询记录失败: %v", err))
                // 不返回错误，继续处理
        }</span>

        <span class="cov0" title="0">return resp, nil</span>
}

// CreatePhoneBalanceRecord 创建手机话费余额记录（仅录入）
func (s *PhoneBalanceService) CreatePhoneBalanceRecord(req request.PhoneBalanceQuery, userId uint) error <span class="cov0" title="0">{
        // 查找是否已存在该手机号的记录
        var existingRecord douyin.PhoneBalance
        err := global.GVA_DB.Where("phone_number = ?", req.PhoneNumber).First(&amp;existingRecord).Error
        if err == nil </span><span class="cov0" title="0">{
                // 记录已存在，返回错误
                return fmt.Errorf("该手机号 %s 已存在", req.PhoneNumber)
        }</span>

        // 准备记录数据（不进行余额查询）
        <span class="cov0" title="0">record := douyin.PhoneBalance{
                PhoneNumber:   req.PhoneNumber,
                OperatorType:  req.OperatorType,
                RealName:      req.RealName,
                Balance:       0,
                QueryTime:     0,
                QueryStatus:   douyin.PhoneBalanceQueryStatusOnlyInput,
                QueryMessage:  "仅录入，未查询",
                RequestIP:     "",
                RequestUserID: userId,
        }

        // 创建新记录
        if err := global.GVA_DB.Create(&amp;record).Error; err != nil </span><span class="cov0" title="0">{
                global.GVA_LOG.Error(fmt.Sprintf("创建手机话费余额记录失败: %v", err))
                return fmt.Errorf("创建记录失败: %w", err)
        }</span>

        <span class="cov0" title="0">return nil</span>
}

// queryPhoneBalanceWithID 查询手机话费余额（指定ID）
func (s *PhoneBalanceService) queryPhoneBalanceWithID(
        req request.PhoneBalanceQuery, userId uint, ip string, recordID uint,
) (*response.PhoneBalanceResponse, error) <span class="cov0" title="0">{
        // 调用公共查询函数获取余额
        balance, err := s.queryPhoneBalanceFromAPI(req.PhoneNumber, req.OperatorType)
        if err != nil </span><span class="cov0" title="0">{
                // 更新查询时间和状态
                global.GVA_DB.Model(&amp;douyin.PhoneBalance{}).Where("id = ?", recordID).
                        Update("query_time", time.Now().Unix()).Update("query_status", douyin.PhoneBalanceQueryStatusFailed)
                return nil, err
        }</span>

        // 构建响应
        <span class="cov0" title="0">resp := &amp;response.PhoneBalanceResponse{
                PhoneNumber:  req.PhoneNumber,
                OperatorType: req.OperatorType,
                RealName:     req.RealName,
                Balance:      balance,
                QueryTime:    time.Now().Unix(),
                QueryStatus:  douyin.PhoneBalanceQueryStatusSuccess,
                QueryMessage: "查询成功",
        }

        // 查找现有记录
        var existingRecord douyin.PhoneBalance
        if err := global.GVA_DB.First(&amp;existingRecord, recordID).Error; err != nil </span><span class="cov0" title="0">{
                global.GVA_LOG.Error(fmt.Sprintf("查找记录失败: %v", err))
                return resp, nil
        }</span>

        // 更新记录字段
        <span class="cov0" title="0">existingRecord.PhoneNumber = req.PhoneNumber
        existingRecord.OperatorType = req.OperatorType
        existingRecord.RealName = req.RealName
        existingRecord.Balance = balance
        existingRecord.QueryTime = resp.QueryTime
        existingRecord.QueryStatus = resp.QueryStatus
        existingRecord.QueryMessage = resp.QueryMessage
        existingRecord.RequestIP = ip
        existingRecord.RequestUserID = userId

        // 保存更新
        if err := global.GVA_DB.Save(&amp;existingRecord).Error; err != nil </span><span class="cov0" title="0">{
                global.GVA_LOG.Error(fmt.Sprintf("更新手机话费余额记录失败: %v", err))
                // 不返回错误，继续处理
        }</span>

        <span class="cov0" title="0">return resp, nil</span>
}

// GetPhoneBalanceList 获取手机话费余额查询记录列表
func (s *PhoneBalanceService) GetPhoneBalanceList(
        req request.PhoneBalanceSearch,
        userIds []uint,
) (list []response.PhoneBalanceInfoResponse, total int64, err error) <span class="cov0" title="0">{
        limit := req.PageSize
        offset := req.PageSize * (req.Page - 1)
        db := global.GVA_DB.Model(&amp;douyin.PhoneBalance{})
        db = db.Where("request_user_id IN (?)", userIds)

        // 添加查询条件
        if req.PhoneNumber != "" </span><span class="cov0" title="0">{
                db = db.Where("phone_number LIKE ?", "%"+req.PhoneNumber+"%")
        }</span>
        <span class="cov0" title="0">if req.OperatorType != "" </span><span class="cov0" title="0">{
                db = db.Where("operator_type = ?", req.OperatorType)
        }</span>
        <span class="cov0" title="0">if req.RealName != "" </span><span class="cov0" title="0">{
                db = db.Where("phone_balance.`real_name` LIKE ?", "%"+req.RealName+"%")
        }</span>
        <span class="cov0" title="0">if req.StartTime &gt; 0 </span><span class="cov0" title="0">{
                db = db.Where("query_time &gt;= ?", req.StartTime)
        }</span>
        <span class="cov0" title="0">if req.EndTime &gt; 0 </span><span class="cov0" title="0">{
                db = db.Where("query_time &lt;= ?", req.EndTime)
        }</span>

        // 获取总数
        <span class="cov0" title="0">err = db.Count(&amp;total).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, 0, err
        }</span>

        // 获取记录，添加sys_user表的关联查询以获取录入者信息
        <span class="cov0" title="0">err = db.Select(`
                phone_balance.*, 
                dy_user.nickname as dy_nickname, 
                dy_user.unique_id as dy_unique_id, 
                flame_user.nickname as flame_nickname, 
                flame_user.unique_id as flame_unique_id, 
                sys_users.nick_name as request_user_name
        `).
                Joins("LEFT JOIN dy_user ON phone_balance.phone_number = dy_user.bind_phone AND dy_user.deleted_at IS NULL").
                Joins("LEFT JOIN flame_user ON phone_balance.phone_number = flame_user.bind_phone AND flame_user.deleted_at IS NULL").
                Joins("LEFT JOIN sys_users ON phone_balance.request_user_id = sys_users.id").
                Limit(limit).Offset(offset).Order("id desc").Find(&amp;list).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, 0, err
        }</span>

        <span class="cov0" title="0">return list, total, nil</span>
}

// DeletePhoneBalanceRecord 删除手机话费余额查询记录
func (s *PhoneBalanceService) DeletePhoneBalanceRecord(id uint) error <span class="cov0" title="0">{
        return global.GVA_DB.Delete(&amp;douyin.PhoneBalance{}, id).Error
}</span>

// GetOperatorTypes 获取支持的运营商类型
func (s *PhoneBalanceService) GetOperatorTypes() []map[string]string <span class="cov0" title="0">{
        return []map[string]string{
                {"value": "mobile", "label": "中国移动"},
                {"value": "unicom", "label": "中国联通"},
                {"value": "telecom", "label": "中国电信"},
                {"value": "35internet", "label": "三五互联"},
        }
}</span>

// RefreshPhoneBalance 刷新手机话费余额
func (s *PhoneBalanceService) RefreshPhoneBalance(id uint, userId uint, ip string) (*response.PhoneBalanceResponse, error) <span class="cov0" title="0">{
        // 查找记录
        var record douyin.PhoneBalance
        if err := global.GVA_DB.First(&amp;record, id).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("找不到记录: %w", err)
        }</span>

        // 构建查询请求
        <span class="cov0" title="0">req := request.PhoneBalanceQuery{
                PhoneNumber:  record.PhoneNumber,
                OperatorType: record.OperatorType,
                RealName:     record.RealName,
        }

        // 调用查询方法，但保留原始记录ID
        resp, err := s.queryPhoneBalanceWithID(req, userId, ip, id)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        <span class="cov0" title="0">return resp, nil</span>
}

// BatchRefreshPhoneBalance 批量刷新手机话费余额
func (s *PhoneBalanceService) BatchRefreshPhoneBalance(ids []uint, userId uint, ip string) ([]response.PhoneBalanceResponse, error) <span class="cov0" title="0">{
        results := make([]response.PhoneBalanceResponse, 0, len(ids))

        for _, id := range ids </span><span class="cov0" title="0">{
                resp, err := s.RefreshPhoneBalance(id, userId, ip)
                if err != nil </span><span class="cov0" title="0">{
                        // 记录错误但继续处理其他记录
                        global.GVA_LOG.Error(fmt.Sprintf("刷新手机话费余额失败 (ID: %d): %v", id, err))
                        continue</span>
                }

                <span class="cov0" title="0">if resp != nil </span><span class="cov0" title="0">{
                        results = append(results, *resp)
                }</span>
        }

        <span class="cov0" title="0">return results, nil</span>
}

// UpdatePhoneBalanceRecord 更新手机话费余额记录
func (s *PhoneBalanceService) UpdatePhoneBalanceRecord(req request.PhoneBalanceUpdate) error <span class="cov0" title="0">{
        // 查找记录
        var record douyin.PhoneBalance
        if err := global.GVA_DB.First(&amp;record, req.ID).Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("找不到记录: %w", err)
        }</span>

        // 更新记录字段
        <span class="cov0" title="0">record.PhoneNumber = req.PhoneNumber
        record.OperatorType = req.OperatorType
        record.RealName = req.RealName

        // 保存更新
        if err := global.GVA_DB.Save(&amp;record).Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("更新手机话费余额记录失败: %w", err)
        }</span>

        <span class="cov0" title="0">return nil</span>
}

// SearchPhone 根据手机号模糊查询数据
func (s *PhoneBalanceService) SearchPhone(phone string, userIds []uint) (list []douyin.PhoneBalance, err error) <span class="cov0" title="0">{
        db := global.GVA_DB.Model(&amp;douyin.PhoneBalance{})
        // 添加模糊查询条件和未删除条件
        db = db.Where("phone_number LIKE ? AND deleted_at IS NULL AND request_user_id IN (?)", "%"+phone+"%", userIds)
        // 获取符合条件的记录
        err = db.Find(&amp;list).Error
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>
        <span class="cov0" title="0">return list, nil</span>
}

// UpdatePhoneBalanceRemark 更新手机话费余额记录备注
func (s *PhoneBalanceService) UpdatePhoneBalanceRemark(id uint, remark string) error <span class="cov0" title="0">{
        // 查找记录
        var record douyin.PhoneBalance
        if err := global.GVA_DB.First(&amp;record, id).Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("找不到记录: %w", err)
        }</span>

        // 更新备注字段
        <span class="cov0" title="0">record.Remark = remark

        // 保存更新
        if err := global.GVA_DB.Save(&amp;record).Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("更新手机话费余额记录备注失败: %w", err)
        }</span>

        <span class="cov0" title="0">return nil</span>
}
</pre>
		
		</div>
	</body>
	<script>
	(function() {
		var files = document.getElementById('files');
		var visible;
		files.addEventListener('change', onChange, false);
		function select(part) {
			if (visible)
				visible.style.display = 'none';
			visible = document.getElementById(part);
			if (!visible)
				return;
			files.value = part;
			visible.style.display = 'block';
			location.hash = part;
		}
		function onChange() {
			select(files.value);
			window.scrollTo(0, 0);
		}
		if (location.hash != "") {
			select(location.hash.substr(1));
		}
		if (!visible) {
			select("file0");
		}
	})();
	</script>
</html>
