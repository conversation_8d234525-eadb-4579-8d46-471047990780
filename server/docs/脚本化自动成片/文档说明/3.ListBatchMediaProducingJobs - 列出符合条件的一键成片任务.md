列出符合条件的一键成片任务，可以根据任务类型、状态等条件查询。

## 授权信息

下表是API对应的授权信息，可以在RAM权限策略语句的`Action`元素中使用，用来给RAM用户或RAM角色授予调用此API的权限。具体说明如下：

- 操作：是指具体的权限点。
- 访问级别：是指每个操作的访问级别，取值为写入（Write）、读取（Read）或列出（List）。
- 资源类型：是指操作中支持授权的资源类型。具体说明如下：
  - 对于必选的资源类型，用前面加 \\* 表示。
  - 对于不支持资源级授权的操作，用`全部资源`表示。
- 条件关键字：是指云产品自身定义的条件关键字。
- 关联操作：是指成功执行操作所需要的其他权限。操作者必须同时具备关联操作的权限，操作才能成功。

| 操作 | 访问级别 | 资源类型 | 条件关键字 | 关联操作 |
| --- | --- | --- | --- | --- |
| ice:ListBatchMediaProducingJobs | list | \*全部资源<br>`*` | 无 | 无 |

## 请求参数

| 名称 | 类型 | 必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| JobId | string | 否 | 一键成片任务 Id | \*\*\*\*d80e4e4044975745c14b\*\*\*\* |
| JobType | string | 否 | 任务类型：<br>- Script(脚本化素材混剪)<br>- Smart\_Mix(智能混编素材混剪) | Script |
| Status | string | 否 | 任务状态：<br>- Finished(处理完成)<br>- Init(初始化)<br>- Failed(失败)<br>- Processing(处理中) | Finished |
| StartTime | string | 否 | 开始时间。UTC 时间，格式：yyyy-MM-ddTHH:mm:ssZ。 | 2022-02-02T00:00:00Z |
| EndTime | string | 否 | 结束时间。UTC 时间，格式：yyyy-MM-ddTHH:mm:ssZ。 | 2023-06-05T15:59:59Z |
| SortBy | string | 否 | 查询结果排序。取值：<br>- desc（默认值）：按创建时间倒序。<br>- asc：按创建时间升序。<br>枚举值：<br>- asc：按照创建时间正序。<br>- desc：按照创建时间倒序。 | desc |
| NextToken | string | 否 | 用来表示当前调用返回读取到的位置，空代表数据已经读取完毕 | mRZkKAovub0xWVfH14he4Q== |
| MaxResults | integer | 否 | 本次请求所返回的最大记录条数 | 100 |

## 返回参数

| 名称 | 类型 | 描述 | 示例值 |
| --- | --- | --- | --- |
|  | object | Schema of Response |  |
| RequestId | string | Id of the request | \*\*\*\*\*\*3B-0E1A-586A-AC29-742247\*\*\*\*\*\* |
| EditingBatchJobList | array<object> | 一键成片任务列表 |  |
| EditingBatchJob | object | 一键成片任务列表 |  |
| JobId | string | 一键成片任务 Id | \*\*\*\*\*\*7ecbee4c6d9b8474498e\*\*\*\*\*\* |
| InputConfig | string | 用户合成输出配置。 | {<br> "MediaGroupArray": \[{<br> "MediaArray": \[<br> "\*\*\*\*9d46c886b45481030f6e\*\*\*\*",<br> "\*\*\*\*6c886b4549d481030f6e\*\*\*\*" \]<br> }, {<br> "MediaArray": \[<br> "\*\*\*\*d46c886810b454930f6e\*\*\*\*",<br> "\*\*\*\*4549d886810b46c30f6e\*\*\*\*" \]<br> }\],<br> "TitleArray": \[<br> "回龙观盒马鲜生开业啦"\],<br> "SpeechTextArray": \[<br> "附近的商场新开了一家盒马鲜生，今天是第一天开业"\]<br>} |
| EditingConfig | string | 剪辑相关配置。具体结构定义，请参见 [EditingConfig](https://help.aliyun.com/zh/ims/use-cases/batch-intelligent-one-key-slice-parameter-description/#1be9bba03b7qu) 配置说明。 | {<br> "MediaConfig": {<br> "Volume": 0<br> },<br> "SpeechConfig": {<br> "Volume": 1<br> },<br> "BackgroundMusicConfig": {<br> "Volume": 0.3<br> }<br>} |
| OutputConfig | string | 输出配置。具体结构定义，请参见 [OutputConfig](https://help.aliyun.com/zh/ims/use-cases/batch-intelligent-one-key-slice-parameter-description/#447b928fcbuoa) 配置说明。 | {<br> "MediaURL": "http://xxx.oss-cn-shanghai.aliyuncs.com/xxx\_{index}.mp4",<br> "Count": 20,<br> "MaxDuration": 15,<br> "Width": 1080,<br> "Height": 1920,<br> "Video": {"Crf": 27}<br>} |
| Status | string | 任务状态。<br>枚举值：<br>- Finished：处理完成。<br>- Init：初始化。<br>- Failed：失败。<br>- Processing：处理中。 | Finished |
| UserData | string | 自定义设置，Json 格式，长度限制为 512 字节。支持 [自定义回调地址配置](https://help.aliyun.com/zh/ims/use-cases/to-configure-a-callback-when-a-clip-completes#section-fbd-xz2-xum)。 | {"NotifyAddress":"http://xx.xx.xxx"}或{"NotifyAddress":"https://xx.xx.xxx"}或{"NotifyAddress":"ice-callback-demo"} |
| Extend | string | 任务扩展信息 | {} |
| CreateTime | string | 创建时间，UTC 时间格式 | 2023-06-09T06:36:48Z |
| ModifiedTime | string | 最后修改时间 | 2023-06-09T06:37:58Z |
| CompleteTime | string | 完成时间。格式为： _yyyy-MM-dd_ T _HH:mm:ss_ Z（UTC 时间）。 | 2023-06-09T06:38:09Z |
| JobType | string | 任务类型<br>枚举值：<br>- Script：脚本化素材混剪。<br>- Smart\_Mix：智能混编素材混剪。 | Script |
| NextToken | string | 下一次查询的开始 Token。 | 8EqYpQbZ6Eh7+Zz8DxVYoQ== |
| MaxResults | integer | 分页大小。最大不超过 100。<br>默认值：10 | 100 |

## 示例

正常返回示例

`JSON` 格式

```json
{
  "RequestId": "******3B-0E1A-586A-AC29-742247******",
  "EditingBatchJobList": [
    {
      "JobId": "******7ecbee4c6d9b8474498e******",
      "InputConfig": "{\n  \"MediaGroupArray\": [{\n      \"MediaArray\": [\n        \"****9d46c886b45481030f6e****\",\n        \"****6c886b4549d481030f6e****\" ]\n    }, {\n      \"MediaArray\": [\n        \"****d46c886810b454930f6e****\",\n        \"****4549d886810b46c30f6e****\" ]\n  }],\n  \"TitleArray\": [\n      \"回龙观盒马鲜生开业啦\"],\n  \"SpeechTextArray\": [\n      \"附近的商场新开了一家盒马鲜生，今天是第一天开业\"]\n}",
      "EditingConfig": "{\n  \"MediaConfig\": {\n      \"Volume\": 0\n  },\n  \"SpeechConfig\": {\n      \"Volume\": 1\n  },\n \"BackgroundMusicConfig\": {\n      \"Volume\": 0.3\n  }\n}",
      "OutputConfig": "{\n  \"MediaURL\": \"http://xxx.oss-cn-shanghai.aliyuncs.com/xxx_{index}.mp4\",\n  \"Count\": 20,\n  \"MaxDuration\": 15,\n  \"Width\": 1080,\n  \"Height\": 1920,\n  \"Video\": {\"Crf\": 27}\n}",
      "Status": "Finished",
      "UserData": "{\"NotifyAddress\":\"http://xx.xx.xxx\"}或{\"NotifyAddress\":\"https://xx.xx.xxx\"}或{\"NotifyAddress\":\"ice-callback-demo\"}",
      "Extend": "{}",
      "CreateTime": "2023-06-09T06:36:48Z",
      "ModifiedTime": "2023-06-09T06:37:58Z",
      "CompleteTime": "2023-06-09T06:38:09Z\n",
      "JobType": "Script"
    }
  ],
  "NextToken": "8EqYpQbZ6Eh7+Zz8DxVYoQ==",
  "MaxResults": 100
}
```

## 错误码

访问[错误中心](https://api.aliyun.com/document/ICE/2020-11-09/errorCode)查看更多错误码。