本文旨在介绍在脚本化自动成片场景下的合成参数、进阶配置、SDK调用示例。

**重要**

- 脚本化自动成片与智能图文匹配成片共用同一个提交任务API。有关如何通过参数区分这两者，请参见 [参数区别说明](https://help.aliyun.com/zh/ims/use-cases/intelligent-one-click-chip-parameter-judgment-rules "")

- **注意：在此接口中，所有媒资的OSS URL中的区域（region）必须与调用OpenAPI服务地址中的区域（region）保持一致。**

- 支持脚本化自动成片的区域：华东2（上海）、华北2（北京）、华东1（杭州）、华南1（深圳）、美国（西部）、新加坡。

- 在实际使用过程中，请将文档所有参数示例中的 \[your-bucket\]、\[your-region-id\]、\[your-file-name\]、\[your-file-path\]、媒资ID（例如：“\*\*\*\*9d46c8b4548681030f6e\*\*\*\*”）等参数替换为您的实际值。


**说明**

- 为了更好地阅读本文，建议您在阅读之前先了解 [智能一键成片](https://help.aliyun.com/zh/ims/user-guide/batch-intelligent-one-click-film/ "") 中与【脚本化自动成片】相关的内容。

- 脚本化自动成片当前存在两种处理模式分别为“全局口播模式”和“分组口播模式”：

  - 全局口播模式：可以通过多个完整的口播文案随机搭配脚本节点，从而实现批量视频混剪。

  - 分组口播模式：可以通过将一个完整的口播文案拆分成多个段落，并分别与脚本的各个节点进行巧妙搭配，以实现更佳的效果。

  - 如何通过参数区分“全局口播模式”和“分组口播模式”，如下所示：

    - 当SpeechTextArray不为空，则视为“全局口播模式”；

    - 当SpeechTextArray为空，且 [MediaGroupArray](https://help.aliyun.com/zh/ims/use-cases/scripted-auto-slice?spm=a2c4g.11186623.0.i0#a8b059ca20leb "") 中只要有一个 [MediaGroup.Duration](https://help.aliyun.com/zh/ims/use-cases/scripted-auto-slice?spm=a2c4g.11186623.0.i0#1de833b1d67ks "") 或 [MediaGroup.SpeechTextArray](https://help.aliyun.com/zh/ims/use-cases/scripted-auto-slice?spm=a2c4g.11186623.0.i0#72ce63cfb70c5 "") 不为空，则视为“分组口播模式”；

    - 当SpeechTextArray为空，且 [MediaGroupArray](https://help.aliyun.com/zh/ims/use-cases/scripted-auto-slice?spm=a2c4g.11186623.0.i0#a8b059ca20leb "") 中所有的 [MediaGroup.Duration](https://help.aliyun.com/zh/ims/use-cases/scripted-auto-slice?spm=a2c4g.11186623.0.i0#1de833b1d67ks "") 和 [MediaGroup.SpeechTextArray](https://help.aliyun.com/zh/ims/use-cases/scripted-auto-slice?spm=a2c4g.11186623.0.i0#72ce63cfb70c5 "") 皆为空，则视为“全局口播模式”；

## 使用说明

- 将多个视频、音频、图片素材进行智能混剪，一键批量合成视频接口说明，请参见 [SubmitBatchMediaProducingJob - 批量智能一键成片](https://help.aliyun.com/zh/ims/developer-reference/api-ice-2020-11-09-submitbatchmediaproducingjob "")，api关键参数详见下文 [InputConfig 参数说明](https://help.aliyun.com/zh/ims/use-cases/scripted-auto-slice?spm=a2c4g.11186623.0.i0#c14ab4f05dftt "")、 [EditingConfig 参数说明](https://help.aliyun.com/zh/ims/use-cases/scripted-auto-slice?spm=a2c4g.11186623.0.i0#4ac5e4fc5eoxr "")、 [OutputConfig 参数说明](https://help.aliyun.com/zh/ims/use-cases/scripted-auto-slice?spm=a2c4g.11186623.0.i0#125382bebd0v5 "")。

- 获取批量智能一键成片作业的详细信息，请参见 [GetBatchMediaProducingJob - 获取批量智能一键成片任务信息](https://help.aliyun.com/zh/ims/developer-reference/api-ice-2020-11-09-getbatchmediaproducingjob "")。


## **InputConfig 参数说明**

**说明**

用户可通过配置InputConfig，指定视频素材、口播、背景音乐、贴纸等基础素材的参数配置。

| **参数**               | **类型**                                                                                                                      | **说明**                                                                                                                                                                                                                                                                      | **示例值**                                                                                                                                                                                                                                       | **是否必填** | **支持模式**               |
| -------------------- | --------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------- | ---------------------- |
| MediaGroupArray      | List< [MediaGroup](https://help.aliyun.com/zh/ims/use-cases/scripted-auto-slice?spm=a2c4g.11186623.0.i0#f8718aa72b1uu "") > | 脚本化自动成片模式。输入为脚本化素材，支持设置分组名、素材列表<br>**分组名：** 不超过50个字符，不支持emoji。<br>**素材列表：** 媒资ID或素材OSS URL。<br>最多40个分组，每组最多200个素材。                                                                                                                                                          | 详见 [全局口播模式-参数示例](https://help.aliyun.com/zh/ims/use-cases/scripted-auto-slice?spm=a2c4g.11186623.0.i0#b3bb51ec4ah75 "")、 [分组口播模式-参数示例](https://help.aliyun.com/zh/ims/use-cases/scripted-auto-slice?spm=a2c4g.11186623.0.i0#9e673b4b826ib "") | 是        | - 全局口播<br>  <br>- 分组口播 |
| TitleArray           | List<String>                                                                                                                | 标题数组，每次合成随机选一个<br>最多50个，每个标题不超过50字                                                                                                                                                                                                                                          | \["标题1","标题2"\]                                                                                                                                                                                                                               | 否        | - 全局口播<br>  <br>- 分组口播 |
| SubHeadingArray      | List< [SubHeading](https://help.aliyun.com/zh/ims/use-cases/batch-video-production-public-parameters#984908fa54ux3 "") >    | 副标题设置                                                                                                                                                                                                                                                                       | \[{"Level":1,"TitleArray":\["一级副标题1","一级副标题2"\]},{"Level":3,"TitleArray":\["三级副标题"\]}\]                                                                                                                                                       | 否        | - 全局口播<br>  <br>- 分组口播 |
| SpeechTextArray      | List<String>                                                                                                                | - 口播文案数组，每次合成随机选一个。<br>  <br>- 最多50个，每条口播文案最长1000个字符。<br>  <br>- 支持通过 [SSML标记语言](https://help.aliyun.com/zh/ims/developer-reference/ssml-markup-language-description "") 控制语音合成<br>  <br>  <br>  <br>  **重要**<br>  <br>  <br>  <br>  <br>  <br>  当前仅支持<break> <s> <phoneme> | \["口播内容1","口播内容2"\]                                                                                                                                                                                                                           | 否        | - 全局口播                 |
| StickerArray         | List< [Sticker](https://help.aliyun.com/zh/ims/use-cases/batch-video-production-public-parameters#0314f87dff9nm "") >       | - 贴纸数组，每次合成随机选一个，最多50个。<br>  <br>- 随机规则：假设传入10张贴纸，成片数量设置为20。首先，随机生成一个1至10之间的数字，例如3，然后按照3、4、5、6、7、8、9、10、1、2、3、4、5、6、7...的顺序选择贴纸。                                                                                                                                            | \[{"MediaId":"\*\*\*\*9d46c8b4548681030f6e\*\*\*\*","X":10,"Y":100,"Width":300,"Height":300,"Opacity":0.6}\]                                                                                                                                  | 否        | - 全局口播<br>  <br>- 分组口播 |
| BackgroundMusicArray | List<String>                                                                                                                | 背景音乐数组，每次合成随机选一个。<br>最多50个，支持媒资ID 或 OSS URL。                                                                                                                                                                                                                                | \["\*\*\*\*b4549d46c88681030f6e\*\*\*\*","\*\*\*\*549d46c88b4681030f6e\*\*\*\*"\]                                                                                                                                                             | 否        | - 全局口播<br>  <br>- 分组口播 |
| BackgroundImageArray | List<String>                                                                                                                | - 背景图片数组，每次合成随机选择一个。最多50个，支持媒资ID 或 OSS URL。<br>  <br>- 随机规则：假设传入10张背景图，成片数量设置为20。首先，随机生成一个1至10之间的数字，例如3，然后按照3、4、5、6、7、8、9、10、1、2、3、4、5、6、7...的顺序选择背景图。                                                                                                                      | \["\*\*\*\*b4549d46c88681030f6e\*\*\*\*","\*\*\*\*549d46c88b4681030f6e\*\*\*\*"\]                                                                                                                                                             | 否        | - 全局口播<br>  <br>- 分组口播 |

### **MediaGroup 参数说明**

**说明**

“全局口播模式”与“分组口播模式”的MediaGroup参数配置差异可根据表格列“支持模式”区分。

| **参数**          | **类型**       | **说明**                                                                                                                                                                                                                                                                                                                                                                                | **示例值**                              | **是否必填**         | **支持模式**               |
| --------------- | ------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------ | ---------------- | ---------------------- |
| GroupName       | String       | 分组名，<br>不超过50个字符，不支持emoji。                                                                                                                                                                                                                                                                                                                                                            | Group1                               | 是                | - 全局口播<br>  <br>- 分组口播 |
| MediaArray      | List<String> | 素材列表，支持 mediaId 或 url。<br>最多支持200个素材。                                                                                                                                                                                                                                                                                                                                                 | \*\*\*\*b4549d46c88681030f6e\*\*\*\* | 是                | - 全局口播<br>  <br>- 分组口播 |
| SpeechTextArray | List<String> | - 口播文案数组，每次合成随机选一个。<br>  <br>- 最多50个，每条口播文案最长1000个字符。<br>  <br>- 支持通过 [SSML标记语言](https://help.aliyun.com/zh/ims/developer-reference/ssml-markup-language-description "") 控制语音合成<br>  <br>  <br>  <br>  **重要**<br>  <br>  <br>  <br>  <br>  <br>  当前仅支持<break> <s> <phoneme>                                                                                                           | \["口播内容1","口播内容2"\]                  | 否                | - 分组口播                 |
| Duration        | Float        | 当前分组对应的时长，单位秒。仅限SpeechTextArray为空时填写。                                                                                                                                                                                                                                                                                                                                                 | 10                                   | 否，默认5            | - 分组口播                 |
| SplitMode       | String       | - 设置分组中视频素材的拆条模式。<br>  <br>- 关于此参数的处理逻辑和用法可参见：<br>  <br>  - [如何确保视频素材在成片中能够被完整播完？](https://help.aliyun.com/zh/ims/support/script-to-video-faq#9e890f382emlt "")<br>    <br>  - [如何解决成片画面切换过于生硬、频率过快的问题？](https://help.aliyun.com/zh/ims/support/script-to-video-faq#064892c4503w9 "")<br>- 取值范围：<br>  <br>  - NoSplit：不拆条<br>    <br>  - AverageSplit：自动拆条，依据参数SingleShotDuration进行拆条 | NoSplit                              | 否，默认AverageSplit | - 全局口播<br>  <br>- 分组口播 |
| Volume          | Float        | - 输入视频的音量。如果在此处设置了音量（Volume），则当前分组内的视频音量将与此处的音量对齐。而 [EditingConfig.MediaConfig.Volume](https://help.aliyun.com/zh/ims/use-cases/batch-video-production-public-parameters#973325e926cqq "") 将不再对此分组生效。<br>  <br>- 取值：\[0, 10.0\]，支持小数点后两位。                                                                                                                                             | 0.5                                  | 否                | - 分组口播                 |

### **全局口播模式-参数示例**

```json
{
  "MediaGroupArray": [
    {
      "GroupName": "UseMediaId",
      "MediaArray": [
        "****9d46c886b45481030f6e****",
        "****c886810b4549d4630f6e****"
      ],
      "SplitMode": "NoSplit"
    },
    {
      "GroupName": "UseOssUrl",
      "MediaArray": [
        "http://[your-bucket].oss-[your-region-id].aliyuncs.com/[your-file-path]/[your-file-name].mp4",
        "http://[your-bucket].oss-[your-region-id].aliyuncs.com/[your-file-path]/[your-file-name].png"
      ]
    }
  ],
  "TitleArray": [
    "回龙观盒马鲜生开业啦",
    "盒马鲜生开业啦"
  ],
  "SubHeadingArray": [
    {
      "Level": 1,
      "TitleArray": ["副标题1", "副标题2"]
    },
    {
      "Level": 3,
      "TitleArray": ["三级副标题"]
    }
  ],
  "SpeechTextArray": [
    "附近的商场新开了一家盒马鲜生，今天是第一天开业，赶紧来凑热闹，这家盒马面积不大，但商场里的人不少，零食、酒水都比较便宜，排队的人都排成了长龙，大家也快来看看呀",
    "附近的商场新开了一家盒马鲜生，今天是第一天开业，赶紧来凑热闹",
    "<speak>战火<phoneme alphabet="py" ph="zheng4 hao3">正酣</phoneme>。今天，我们的主角，乒坛传奇马龙，正向着荣耀的巅峰发起冲击。1/4决赛中面对实力强劲的户上隼辅，马龙毫不畏惧，每一个回合都全力以赴。他精准的球路和冷静的判断，让他在这场比赛中占据了上风。最终，马龙成功战胜对手，晋级四强。</speak>"
  ],
  "StickerArray": [
    {
      "MediaId": "****9d46c8b4548681030f6e****",
      "X": 10,
      "Y": 100,
      "Width": 300,
      "Height": 300,
      "Opacity": 0.6
    },
    {
      "MediaURL": "http://[your-bucket].oss-[your-region-id].aliyuncs.com/[your-file-path]/[your-file-name].png",
      "X": 10,
      "Y": 100,
      "Width": 300,
      "Height": 300
    }
  ],
  "BackgroundMusicArray": [
    "****b4549d46c88681030f6e****",
    "****549d46c88b4681030f6e****",
    "http://[your-bucket].oss-[your-region-id].aliyuncs.com/[your-file-path]/[your-file-name].mp3"
  ],
  "BackgroundImageArray": [
    "****6c886b4549d481030f6e****",
    "****9d46c8548b4681030f6e****",
    "http://[your-bucket].oss-[your-region-id].aliyuncs.com/[your-file-path]/[your-file-name].png"
  ]
}
```

### **分组口播模式-参数示例**

```json
{
  "MediaGroupArray": [{
    "GroupName": "start",
    "MediaArray": ["https://[your-bucket].oss-[your-region-id].aliyuncs.com/[your-file-path]/[your-file-name].jpeg", "https://[your-bucket].oss-[your-region-id].aliyuncs.com/[your-file-path]/[your-file-name].mp4"],
    "Duration": 5,
    "SplitMode": "NoSplit",
    "Volume": 1
  },
    {
      "GroupName": "group1",
      "MediaArray": ["https://[your-bucket].oss-[your-region-id].aliyuncs.com/[your-file-path]/[your-file-name].png", "https://[your-bucket].oss-[your-region-id].aliyuncs.com/[your-file-path]/[your-file-name].mp4"],
      "SpeechTextArray": ["附近的商场新开了一家盒马鲜生，今天是第一天开业", "今天是这家盒马鲜生第一天开业", "<speak>战火<phoneme alphabet="py" ph="zheng4 hao3">正酣</phoneme>。今天，我们的主角，乒坛传奇马龙，正向着荣耀的巅峰发起冲击。1/4决赛中面对实力强劲的户上隼辅，马龙毫不畏惧，每一个回合都全力以赴。他精准的球路和冷静的判断，让他在这场比赛中占据了上风。最终，马龙成功战胜对手，晋级四强。</speak>"]
    },
    {
      "GroupName": "group2",
      "MediaArray": ["https://[your-bucket].oss-[your-region-id].aliyuncs.com/0-test-batch-editing-materials/normal%20video.mp4", "https://[your-bucket].oss-[your-region-id].aliyuncs.com/[your-file-path]/[your-file-name].jpeg"],
      "SpeechTextArray": ["这家盒马面积不大，但商场里的人不少，零食、酒水都比较便宜，排队的人都排成了长龙", "现场特别热闹，人山人海，商品琳琅满目"]
    },
    {
      "GroupName": "group3",
      "MediaArray": ["https://[your-bucket].oss-[your-region-id].aliyuncs.com/0-test-batch-editing-materials/young_sunset_walk.mp4"],
      "SpeechTextArray": ["快来看看吧", "快点来看看吧"]
    },
    {
      "GroupName": "end",
      "MediaArray": ["https://[your-bucket].oss-[your-region-id].aliyuncs.com/[your-file-path]/[your-file-name].jpg", "https://[your-bucket].oss-[your-region-id].aliyuncs.com/[your-file-path]/[your-file-name].mp4"],
      "Duration": 5
    }
  ],
  "TitleArray": [
    "回龙观盒马鲜生开业啦",
    "盒马鲜生开业啦"
  ],
  "StickerArray": [
    {
      "MediaId": "****9d46c8b4548681030f6e****",
      "X": 10,
      "Y": 100,
      "Width": 300,
      "Height": 300,
      "Opacity": 0.6
    },
     {
      "MediaURL": "http://[your-bucket].oss-[your-region-id].aliyuncs.com/[your-file-path]/[your-file-name].png",
      "X": 10,
      "Y": 100,
      "Width": 300,
      "Height": 300
    }
  ],
  "SubHeadingArray": [
    {
      "Level": 1,
      "TitleArray": ["一级副标题1", "一级副标题2"]
    },
    {
      "Level": 3,
      "TitleArray": ["三级副标题"]
    }
  ],
  "BackgroundMusicArray": [
    "****b4549d46c88681030f6e****",
    "****549d46c88b4681030f6e****",
    "http://[your-bucket].oss-[your-region-id].aliyuncs.com/[your-file-path]/[your-file-name].mp3"
  ],
  "BackgroundImageArray": [
    "****6c886b4549d481030f6e****",
    "****9d46c8548b4681030f6e****",
    "http://[your-bucket].oss-[your-region-id].aliyuncs.com/[your-file-path]/[your-file-name].png"
  ]
}
```

## **EditingConfig 参数说明**

用户可通过配置EditingConfig，指定成片素材的音量、位置及其他合成参数。参数示例请参见： [EditingConfig 参数示例](https://help.aliyun.com/zh/ims/use-cases/scripted-auto-slice?spm=a2c4g.11186623.0.i0#18198697e9qh2 "")

**说明**

除了以下参数外，其余参数皆支持“全局口播模式”和“分组口播模式”：

- [ProcessConfig.AlignmentMode](https://help.aliyun.com/zh/ims/use-cases/scripted-auto-slice?spm=a2c4g.11186623.0.i0#4906a0855e82z "") 仅在“全局口播模式”下生效；

- [SpeechConfig.SpecialWordsConfig](https://help.aliyun.com/zh/ims/use-cases/batch-video-production-public-parameters#516701dd5bhob "") 仅在“分组口播模式”下生效；

| **参数**                                                                                                                      | **类型** | **说明**                                                                                                                                                                                                                                                                              | **示例值**                                                                                                                                                                                                                                                                | **是否必填** |
| --------------------------------------------------------------------------------------------------------------------------- | ------ | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------- |
| [MediaConfig](https://help.aliyun.com/zh/ims/use-cases/batch-video-production-public-parameters#8b7802d783hpj "")           | JSON   | 输入视频素材相关配置。                                                                                                                                                                                                                                                                         | {"Volume":"1","MediaMetaDataArray":\[{"Media":"\*\*\*\*6c886b4549d481030f6e\*\*\*\*","GroupName":"GroupA","TimeRangeList":\[{"In":"0","Out":"1"},{"In":"2","Out":"3"}\]}\]}                                                                                            | 否        |
| [TitleConfig](https://help.aliyun.com/zh/ims/use-cases/batch-video-production-public-parameters#0d4adef6ffvk3 "")           | JSON   | 标题相关配置，支持配置字幕参数。                                                                                                                                                                                                                                                                    | {"Alignment":"TopCenter","AdaptMode":"AutoWrap","Font":"Alibaba PuHuiTi 2.0 95 ExtraBold","SizeRequestType":"Nominal","Y":0.1}                                                                                                                                         | 否        |
| SubHeadingConfig                                                                                                            | JSON   | 多级副标题相关配置。支持设置字幕参数。<br>JSON字段说明：<br>- key： [Level](https://help.aliyun.com/zh/ims/use-cases/batch-video-production-public-parameters#fe64f3c066vkp "")<br>  <br>- value： [横幅文字](https://help.aliyun.com/zh/ims/use-cases/batch-video-production-public-parameters#0d4adef6ffvk3 "") | {"1":{"Y":0.3,"FontSize":40},"3":{"Y":0.5,"FontSize":30}}                                                                                                                                                                                                              | 否        |
| [SpeechConfig](https://help.aliyun.com/zh/ims/use-cases/batch-video-production-public-parameters#d17d42121cm05 "")          | JSON   | 口播文案相关配置。                                                                                                                                                                                                                                                                           | 详见 [EditingConfig 参数示例](https://help.aliyun.com/zh/ims/use-cases/scripted-auto-slice?spm=a2c4g.11186623.0.i0#18198697e9qh2 "")                                                                                                                                         | 否        |
| [BackgroundMusicConfig](https://help.aliyun.com/zh/ims/use-cases/batch-video-production-public-parameters#8068cccf34dyi "") | JSON   | 背景音乐相关配置。                                                                                                                                                                                                                                                                           | {"Volume":0.2}                                                                                                                                                                                                                                                         | 否        |
| [BackgroundImageConfig](https://help.aliyun.com/zh/ims/use-cases/batch-video-production-public-parameters#0fac889a3d2jh "") | JSON   | 背景图相关配置。如果InputConfig中已配置背景图，则此字段不生效。                                                                                                                                                                                                                                               | {"SubType":"Blur","Radius":0.5}                                                                                                                                                                                                                                        | 否        |
| [ProcessConfig](https://help.aliyun.com/zh/ims/use-cases/scripted-auto-slice?spm=a2c4g.11186623.0.i0#ce4715a124eut "")      | JSON   | 混剪处理配置。                                                                                                                                                                                                                                                                             | 详见 [EditingConfig 参数示例](https://help.aliyun.com/zh/ims/use-cases/scripted-auto-slice?spm=a2c4g.11186623.0.i0#18198697e9qh2 "")                                                                                                                                         | 否        |
| [FECanvas](https://help.aliyun.com/zh/ims/use-cases/batch-video-production-template-parameters#69cf25e5c95ow "")            | JSON   | 用于前端页面预览时的画布配置。                                                                                                                                                                                                                                                                     | {"Width": 1080,"Height": 1920}                                                                                                                                                                                                                                         | 否        |
| ProduceConfig                                                                                                               | JSON   | 普通剪辑合成配置，字段详见： [EditingProduceConfig](https://help.aliyun.com/zh/ims/developer-reference/clip-composition-parameter-description#title-10z-t9u-n69 "")                                                                                                                               | {"AutoRegisterInputVodMedia":true,"OutputWebmTransparentChannel":true,"CoverConfig":{"StartTime":3.3},"AudioChannelCopy":"left","PipelineId":"\*\*\*\*d54a97cff4108b555b01166d4\*\*\*\*","MaxBitrate":5000,"KeepOriginMaxBitrate":false,"KeepOriginVideoMaxFps":false} | 否        |

### **ProcessConfig 参数说明**

| **参数**               | **类型**       | **说明**                                                                                                                                                                                                                                                                                                                        | **示例值**                         | **是否必填**      |
| -------------------- | ------------ | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------- | ------------- |
| SingleShotDuration   | Float        | 长视频素材进行剪辑时会自动拆条，拆条后单镜头的时长，单位秒。                                                                                                                                                                                                                                                                                                | 5                               | 否，默认3         |
| AllowVfxEffect       | Boolean      | - 是否允许添加特效效果，特效示例详见： [特效效果示例](https://help.aliyun.com/zh/ims/developer-reference/example-of-common-effects "")。<br>  <br>- 成片中第一个视频片段的特效随机选择范围："slightshow", "starfieldshinee", "starfieldshinee2", "starsparkle", "colorfulripples", "starfield"<br>  <br>- 其余视频片段的特效随机选择范围："zoomslight", "zoom", "zoominout", "slightshake" | true                            | 否，默认false     |
| VfxEffectProbability | Float        | 特效应用在每个视频片段上的概率，取值：0.0 - 1.0，支持2位小数。                                                                                                                                                                                                                                                                                          | 0.6                             | 否，默认0.5       |
| AllowTransition      | Boolean      | 是否允许添加转场效果。                                                                                                                                                                                                                                                                                                                   | true                            | 否，默认false     |
| TransitionDuration   | Float        | 转场时长，单位秒。如果转场时长 \> 片段时长 \- 1，则该片段上的转场效果不会生效。                                                                                                                                                                                                                                                                                  | 0.5                             | 否，默认0.5秒      |
| TransitionList       | List<String> | 自定义转场效果列表，当AllowTransition=true时，随机选取列表中的一个转场效果进行合成，转场效果的可选范围详见 [转场效果库](https://help.aliyun.com/zh/ims/developer-reference/normal-transition-effect-example "")。如果传此参数null，则会从以下转场效果中随机选取："linearblur", "colordistance", "crosshatch", "dreamyzoom", "doomscreentransition\_up"                                               | \["directional", "linearblur"\] | 否             |
| UseUniformTransition | Boolean      | 单个成片中是否使用一致的转场效果。                                                                                                                                                                                                                                                                                                             | true                            | 否，默认true      |
| AlignmentMode        | String       | 表示视频和口播文案的对齐模式。 **仅在“全局口播模式”下生效。** 取值：<br>- "AutoSpeed"：视频轨道时长按照音频轨道缩放。<br>  <br>- "Cut"：视频轨道时长按照音频轨道截断。                                                                                                                                                                                                                      | AutoSpeed                       | 否，默认AutoSpeed |
| ImageDuration        | Float        | 图片素材的持续时长，单位秒。                                                                                                                                                                                                                                                                                                                | 2                               | 否，默认2         |

### EditingConfig **参数示例**

```json
{
  "MediaConfig": {
    "Volume": 0 // 默认视频素材静音
  },
  "TitleConfig": {
    "Alignment": "TopCenter",
    "AdaptMode": "AutoWrap",
    "Font": "Alibaba PuHuiTi 2.0 95 ExtraBold",
    "SizeRequestType": "Nominal",
    "Y": 0.1, // 成片为竖屏时，标题的默认Y坐标值
    "Y": 0.05, // 成片为横屏时，标题的默认Y坐标值
    "Y": 0.08 // 成片为方屏时，标题的默认Y坐标值
  },
   "SubHeadingConfig": {
    "1": {
      "Y": 0.3,
      "FontSize": 40
    },
    "3": {
      "Y": 0.5,
      "FontSize": 30
    }
  },
  "SpeechConfig": {
    "Volume": 1,  // 口播音频默认用原始音量
    "SpeechRate": 0,
    "Voice": null,
    "Style": null,
    "CustomizedVoice": null, // 人声克隆voiceId，若填写了此字段，Voice和Style将失效。
    "AsrConfig": {
      "Alignment": "TopCenter",
      "AdaptMode": "AutoWrap",
      "Font": "Alibaba PuHuiTi 2.0 65 Medium",
      "SizeRequestType": "Nominal",
      "Spacing": -1,
      "Y": 0.8, // 成片为竖屏时，字幕Y的默认坐标值
      "Y": 0.9, // 成片为横屏时，字幕Y的默认坐标值
      "Y": 0.85 // 成片为方屏时，字幕Y的默认坐标值
    },
    "SpecialWordsConfig": [{
      "Type": "Highlight",
      "Style": {
        "FontName": "KaiTi",
        "FontSize": 80,
        "FontColor": "20AEE9",
        "OutlineColour": "2D20E9",
        "Outline": 3,
        "FontFace": {
          "Bold": true,
          "Underline": true
        }
      },
      "WordsList": [
        "视频云",
        "智能媒体服务",
        "智能一键成片"
      ]
    },
    {
      "Type": "Highlight",
      "Style": {
        "FontFace": {
          "Italic": true
        }
      },
      "WordsList": [
        "商品",
        "看看"
      ]
    },
    {
      "Type": "Forbidden",
      "WordsList": [
        "噼里啪啦",
        "哔哩吧啦"
      ],
      "SoundReplaceMode": "None"
    }
  ]},
  "BackgroundMusicConfig": {
    "Volume": 0.2,   // 背景音乐默认用20%音量,
    "Style": null
  },
  "ProcessConfig": {
    "SingleShotDuration": 3,      // 拆条后的镜头时长
    "AllowVfxEffect": false,	  // 是否添加特效效果
    "AllowTransition": false,	  // 是否添加转场效果
    "AlignmentMode": "AutoSpeed"  // 仅在全局口播模式下支持此字段
  }
}
```

## **TemplateConfig 参数说明**

TemplateConfig为一键成片的公共参数，用于设置一键成片模板。详细参数说明和使用示例详见 [TemplateConfig 参数说明](https://help.aliyun.com/zh/ims/use-cases/batch-video-production-public-parameters#32c3bea6182sy "")

## **OutputConfig 参数说明**

用户可通过配置OutputConfig，指定成片输出地址、名称规则、成片的宽高、输出成片数量等合成参数。

**说明**

“全局口播模式”与“分组口播模式”的OutputConfig参数配置说明是相同的。

| **参数**                                                                                                              | **类型**  | **说明**                                                                                                                                                                                                                                                                                                                                                                                         | **示例值**                                                                                                                                                                                         | **是否必填**                                |
| ------------------------------------------------------------------------------------------------------------------- | ------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------- |
| MediaURL                                                                                                            | String  | 输出视频地址，必须要有占位符 {index}。                                                                                                                                                                                                                                                                                                                                                                        | 规则：http://\[your-bucket\].oss-\[your-region-id\].aliyuncs.com/\[your-file-path\]/\[your-file-name\]\_{index}.mp4<br>示例：http://example.oss-cn-shanghai.aliyuncs.com/example/example\_{index}.mp4 | 当GeneratePreviewOnly=true时，且成片输出到OSS时必填 |
| StorageLocation                                                                                                     | String  | 指定输出到VOD的媒资文件存储地址.                                                                                                                                                                                                                                                                                                                                                                             | 规则：\[your-vod-bucket\].oss-\[your-region-id\].aliyuncs.com<br>示例：outin-\*\*\*\*6c886b4549d481030f6e\*\*\*\*.oss-cn-shanghai.aliyuncs.com                                                        | 当GeneratePreviewOnly=true时，且成片输出到VOD时必填 |
| FileName                                                                                                            | String  | 输出文件名称，必须要有占位符{index}.                                                                                                                                                                                                                                                                                                                                                                         | 规则：\[your-file-name\]\_\_{index}.mp4<br>示例：example\_{index}.mp4                                                                                                                                 | 当GeneratePreviewOnly=true时，且成片输出到VOD时必填 |
| GeneratePreviewOnly                                                                                                 | Boolean | - GeneratePreviewOnly = true时，表示当前任务仅生成预览用的时间线，不实际合成，可不填写输出视频的地址。<br>  <br>- 一键成片任务完成后，通过 [GetBatchMediaPoducingJob](https://help.aliyun.com/zh/ims/developer-reference/api-ice-2020-11-09-getbatchmediaproducingjob "") 查询任务结果，返回的子任务列表中会包含剪辑工程projectId，再调用 [GetEditingProject](https://help.aliyun.com/zh/ims/developer-reference/api-ice-2020-11-09-geteditingproject "") 可获取到预览时间线。       | false                                                                                                                                                                                           | 否，默认false                               |
| Count                                                                                                               | Integer | 输出视频数，数量上限为100。                                                                                                                                                                                                                                                                                                                                                                                | 10                                                                                                                                                                                              | 否，默认1                                   |
| MaxDuration                                                                                                         | Float   | 输出视频单片时长上限，单位秒。<br>- 如果有确定「口播文本」参数，以口播文本tts时长为准，当前参数无效。<br>  <br>- 分组口播模式无需设置该参数；<br>  <br>- FixedDuration和MaxDuration只能二选一；<br>  <br>- 此参数的处理逻辑和使用方式详见：<br>  <br>  - [全局口播模式的处理逻辑是什么？](https://help.aliyun.com/zh/ims/support/script-to-video-faq#4c16c06050n2c "")<br>    <br>  - [如何解决画面切换速度过快或过慢以及配置镜头时长的问题？](https://help.aliyun.com/zh/ims/support/script-to-video-faq#160ef6e450jny "") | 20                                                                                                                                                                                              | 否，默认15                                  |
| FixedDuration                                                                                                       | Float   | 输出视频单片的固定时长，单位秒。如果设置了固定时长，视频时长将会对齐此参数。<br>- 分组口播模式不支持设置该参数；<br>  <br>- 全局口播模式下，在SpeechTextArray为空的情况下可支持设置此参数；<br>  <br>- FixedDuration和MaxDuration只能二选一；<br>  <br>- 时长规则详见 [成片时长规则](https://help.aliyun.com/zh/ims/support/script-to-video-faq#4c8e09c4d8biw "")                                                                                                                              | 20                                                                                                                                                                                              | 否，默认15                                  |
| Width                                                                                                               | Integer | 成片宽，px                                                                                                                                                                                                                                                                                                                                                                                         | 1080                                                                                                                                                                                            | 是                                       |
| Height                                                                                                              | Integer | 成片高，px                                                                                                                                                                                                                                                                                                                                                                                         | 1920                                                                                                                                                                                            | 是                                       |
| [Video](https://help.aliyun.com/zh/ims/developer-reference/clip-composition-parameter-description#6525ded0c9jez "") | JSON    | 输出视频流相关配置，Crf、Codec等                                                                                                                                                                                                                                                                                                                                                                           | {"Crf": 27}                                                                                                                                                                                     | 否                                       |

### **参数示例**

```json
{
  "MediaURL": "http://[your-bucket].oss-[your-region-id].aliyuncs.com/[your-file-path]/[your-file-name]_{index}.mp4",
  "Count": 20,
  "MaxDuration": 15,
  "Width": 1080,
  "Height": 1920,
  "Video": {
    "Crf": 27
  },
  "GeneratePreviewOnly": false
}

```

## **应用示例**

### 示例一：通过口播分组模式配置片头片尾

#### 适用场景

如果您希望为视频添加一致的片头和片尾，并配备统一的口播，请参阅该场景的示例。您可以通过将首尾分组的 [MediaGroup.SplitMode](https://help.aliyun.com/zh/ims/use-cases/scripted-auto-slice?spm=a2c4g.11186623.0.i0#8567004375kz5 "") 设置为NoSplit。此时，系统将不再对首尾分组中的素材进行拆条，而是将从首尾中随机选取的素材进行完整播放，以实现添加固定片头和片尾的效果。

#### 示例参数

**展开查看InputConfig参数示例**

```json
{
    "mediaGroupArray": [
        {
            "duration": 4,
            "splitMode": "NoSplit",
            "groupName": "opening",
            "mediaArray": [
                "****e44009ee71f0b62bf6f7d44b****"
            ]
        },
        {
            "groupName": "group1",
            "mediaArray": [
                "****e44009eef1f0b62bf6f7d44b****"
            ],
            "speechTextArray": [
                "假期纠结去哪儿玩？",
                "假期规划还在犹豫不决？"
            ]
        },
        {
            "groupName": "group2",
            "mediaArray": [
                "****e44009eeferfb62bf6f7d44b****",
                "****e440094fghf0b62bf6f7d44b****",
                "****e44009ee74fgh62bf6f7d44b****"
            ],
            "speechTextArray": [
                "云南泸沽湖邀您共赴一场与自然的约会。湛蓝湖水如镜，映照着摩梭女儿国的独特风情，如诗如画。泛舟湖心，感受猪槽船摇曳的岁月静好；仰望XX山，****神秘的故事传说。还在等什么",
                "何不考虑赴一场云南泸沽湖的自然盛宴。那湛蓝如镜的湖面，映射出摩梭女儿国独特的民俗风情，如诗如画，引人入胜。您可在湖心悠然泛舟，体验猪槽船摇曳间的宁静岁月；也可抬头瞻仰神圣XX山，倾听那穿越千年的古老神秘传说。快来泸沽湖"
            ]
        },
        {
            "groupName": "group3",
            "mediaArray": [
                "****e44009ee7ft5662bf6f7d44b****"
            ],
            "speechTextArray": [
                "快来泸沽湖共享这一份静谧而迷人的湖光山色吧！",
                "共享这片静谧而迷人的湖光山色所带来的无尽诗意吧！"
            ]
        },
        {
            "duration": 4,
            "splitMode": "NoSplit",
            "groupName": "ending",
            "mediaArray": [
                "****e44009ee5fgfg62bf6f7d44b****"
            ]
        }
    ]
}
```

**展开查看EditingConfig参数示例**

```json
{
  "MediaConfig": {
    "MediaMetaDataArray": [
      {
        "Media": "****e44009eedttg62bf6f7d44b****",
        "GroupName": "opening",
        "TimeRangeList": [
          {
            "In": 1.5,
            "Out": 5.5
          }
        ]
      },
      {
        "Media": "****e44009ee7dfrf62bf6f7d44b****",
        "GroupName": "ending",
        "TimeRangeList": [
          {
            "In": 1.5,
            "Out": 5.5
          }
        ]
      }
    ]
  }
}

```

**展开查看OutputConfig参数示例**

```json
{
    "count": 10,
    "height": 1920,
    "mediaURL": "http://[your-bucket].oss-[your-region-id].aliyuncs.com/[your-file-path]/[your-file-name]_{index}.mp4",
    "width": 1080,
    "widthHeightRatio": 0.5625
}
```

#### 示例展示

脚本化自动成片

### 示例二：通过脚本化自动成片制作人脸集锦视频

如果您对人脸集锦场景相关的需求感兴趣，建议您查阅相关最佳实践： [人脸集锦视频制作最佳实践](https://help.aliyun.com/zh/ims/use-cases/create-face-montage-videos#c0d3853df2xhe "")。

## **SDK调用示例**

### **前提条件**

您已安装IMS服务端SDK，详情请参见 [准备工作](https://help.aliyun.com/zh/ims/use-cases/audio-and-video-editing-and-synthesis "")。

### 代码示例

以全局口播模式为例

**展开查看代码示例**

```java
package com.example;

import java.util.*;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.aliyun.ice20201109.Client;
import com.aliyun.ice20201109.models.*;
import com.aliyun.teaopenapi.models.Config;

/**
 *  需要maven引入二方包依赖：
 *   <dependency>
 *      <groupId>com.aliyun</groupId>
 *      <artifactId>ice20201109</artifactId>
 *      <version>2.3.0</version>
 *  </dependency>
 *  <dependency>
 *      <groupId>com.alibaba</groupId>
 *      <artifactId>fastjson</artifactId>
 *      <version>1.2.9</version>
 *  </dependency>
 */
public class ScriptBatchEditingService {

    static final String regionId = "<service-region>";
    static final String bucket = "[your-bucket]";
    private Client iceClient;

    public static void main(String[] args) throws Exception {
        ScriptBatchEditingService scriptBatchEditingService = new ScriptBatchEditingService();
        scriptBatchEditingService.initClient();
        scriptBatchEditingService.runExample();
    }

    public void initClient() throws Exception {
        // 阿里云账号AccessKey拥有所有API的访问权限，建议您使用RAM用户进行API访问或日常运维。
        // 本示例以将AccessKey ID和 AccessKey Secret保存在环境变量为例说明。配置方法请参见：https://help.aliyun.com/zh/sdk/developer-reference/v2-manage-access-credentials?spm=a2c4g.11186623.0.0.423350fbOTFdOB#2a38e5c14b4em
        com.aliyun.credentials.Client credentialClient = new com.aliyun.credentials.Client();

        Config config = new Config();
        config.setCredential(credentialClient);

        // 如需硬编码AccessKey ID和AccessKey Secret，代码如下，但强烈建议不要把AccessKey ID和AccessKey Secret保存到工程代码里，否则可能导致AccessKey泄露，威胁您账号下所有资源的安全。
        // config.accessKeyId = <第二步创建的AccessKey ID>;
        // config.accessKeySecret = <第二步创建的AccessKey Secret>;
        config.endpoint = "ice." + regionId + ".aliyuncs.com";
        config.regionId = regionId;
        iceClient = new Client(config);
    }

    public void runExample() throws Exception {

        // 视频素材
        JSONObject mediaGroup1 = new JSONObject();
        mediaGroup1.put("GroupName", "start");
        mediaGroup1.put("MediaArray", Arrays.asList(
            "http://[your-bucket].oss-[your-region-id].aliyuncs.com/test_media/lgh/lgh-start-1.mp4"
        ));

        JSONObject mediaGroup2 = new JSONObject();
        mediaGroup2.put("GroupName", "middle");
        mediaGroup2.put("MediaArray", Arrays.asList(
            "http://[your-bucket].oss-[your-region-id].aliyuncs.com/test_media/lgh/lgh-m-1.mp4",
            "http://[your-bucket].oss-[your-region-id].aliyuncs.com/test_media/lgh/lgh-m-2.mp4",
            "http://[your-bucket].oss-[your-region-id].aliyuncs.com/test_media/lgh/lgh-m-3.mp4"
        ));

        JSONObject mediaGroup3 = new JSONObject();
        mediaGroup3.put("GroupName", "end");
        mediaGroup3.put("MediaArray", Arrays.asList(
            "http://[your-bucket].oss-[your-region-id].aliyuncs.com/test_media/lgh/lgh-end-1.mp4"
        ));

        JSONArray mediaGroupArray = new JSONArray();
        mediaGroupArray.add(mediaGroup1);
        mediaGroupArray.add(mediaGroup2);
        mediaGroupArray.add(mediaGroup3);

        // 口播文案
        List<String> speechTextArray = Arrays.asList(
            "假期纠结去哪儿玩？云南泸沽湖邀您共赴一场与自然的约会。湛蓝湖水如镜，映照着摩梭女儿国的独特风情，如诗如画。泛舟湖心，感受猪槽船摇曳的岁月静好；仰望XX山，****神秘的故事传说。还在等什么，快来泸沽湖共享这一份静谧而迷人的湖光山色吧！",
            "假期规划还在犹豫不决？何不考虑赴一场云南泸沽湖的自然盛宴。那湛蓝如镜的湖面，映射出摩梭女儿国独特的民俗风情，如诗如画，引人入胜。您可在湖心悠然泛舟，体验猪槽船摇曳间的宁静岁月；也可抬头瞻仰神圣XX山，倾听那穿越千年的古老神秘传说。快来泸沽湖，共享这片静谧而迷人的湖光山色所带来的无尽诗意吧！"
        );

        // 视频标题
        List<String> titleArray = Arrays.asList(
            "泸沽湖：湖光山色中的摩梭风情",
            "探寻秘境泸沽湖",
            "沉浸式体验泸沽湖"
        );

        JSONObject inputConfig = new JSONObject();
        inputConfig.put("MediaGroupArray", mediaGroupArray);
        inputConfig.put("SpeechTextArray", speechTextArray);
        inputConfig.put("TitleArray", titleArray);

        // 生成的成片数
        int produceCount = 4;

        // 成片宽高，生成竖屏文件
        //int outputWidth = 1080;
        //int outputHeight = 1920;

        //// 成片宽高，生成横屏文件
        int outputWidth = 1920;
        int outputHeight = 1080;

        // 成片oss地址，需包含{index} 占位符
        String mediaUrl = "http://" + bucket + ".oss-" + regionId + ".aliyuncs.com/script/output_{index}_w.mp4";

        JSONObject outputConfig = new JSONObject();
        outputConfig.put("MediaURL", mediaUrl);
        outputConfig.put("Count", produceCount);
        outputConfig.put("Width", outputWidth);
        outputConfig.put("Height", outputHeight);

        // 提交一键成片任务
        SubmitBatchMediaProducingJobRequest request = new SubmitBatchMediaProducingJobRequest();
        request.setInputConfig(inputConfig.toJSONString());
        request.setOutputConfig(outputConfig.toJSONString());

        SubmitBatchMediaProducingJobResponse response = iceClient.submitBatchMediaProducingJob(request);
        String jobId = response.getBody().getJobId();
        System.out.println("Start script batch job, batchJobId: " + jobId);

        // 轮询任务状态直到全部结束
        System.out.println("Waiting job finished...");
        int maxTry = 3000;
        int i = 0;
        while (i < maxTry) {
            Thread.sleep(3000);
            i++;
            GetBatchMediaProducingJobRequest getRequest = new GetBatchMediaProducingJobRequest();
            getRequest.setJobId(jobId);
            GetBatchMediaProducingJobResponse getResponse = iceClient.getBatchMediaProducingJob(getRequest);
            String status = getResponse.getBody().getEditingBatchJob().getStatus();
            System.out.println("BatchJobId: " + jobId + ", status:" + status);

            if ("Failed".equals(status)) {
                System.out.println("Batch job failed. JobInfo: " + JSONObject.toJSONString(getResponse.getBody().getEditingBatchJob()));
                throw new Exception("Produce failed. BatchJobId: " + jobId);
            }

            if ("Finished".equals(status)) {
                System.out.println("Batch job finished. JobInfo: " + JSONObject.toJSONString(getResponse.getBody().getEditingBatchJob()));
                break;
            }
        }
    }
}

```

**API调用入参详情**

**展开查看InputConfig**

```json
{
  "MediaGroupArray": [
    {
    "GroupName": "start",
    "MediaArray": [
      "http://[your-bucket].oss-[your-region-id].aliyuncs.com/[your-file-path]/[your-file-name].mp4"
      ]
    },
    {
      "GroupName": "middle",
      "MediaArray": [
        "http://[your-bucket].oss-[your-region-id].aliyuncs.com/[your-file-path]/[your-file-name].mp4",
        "http://[your-bucket].oss-[your-region-id].aliyuncs.com/[your-file-path]/[your-file-name].mp4",
        "http://[your-bucket].oss-[your-region-id].aliyuncs.com/[your-file-path]/[your-file-name].mp4"
      ]
    },
    {
      "GroupName": "end",
      "MediaArray": [
        "http://[your-bucket].oss-[your-region-id].aliyuncs.com/[your-file-path]/[your-file-name].mp4"
      ]
    }
  ],
  "SpeechTextArray": [
    "假期纠结去哪儿玩？云南泸沽湖邀您共赴一场与自然的约会。湛蓝湖水如镜，映照着摩梭女儿国的独特风情，如诗如画。泛舟湖心，感受猪槽船摇曳的岁月静好；仰望XX山，****神秘的故事传说。还在等什么，快来泸沽湖共享这一份静谧而迷人的湖光山色吧！",
    "假期规划还在犹豫不决？何不考虑赴一场云南泸沽湖的自然盛宴。那湛蓝如镜的湖面，映射出摩梭女儿国独特的民俗风情，如诗如画，引人入胜。您可在湖心悠然泛舟，体验猪槽船摇曳间的宁静岁月；也可抬头瞻仰神圣XX山，倾听那穿越千年的古老神秘传说。快来泸沽湖，共享这片静谧而迷人的湖光山色所带来的无尽诗意吧！"
  ],
  "TitleArray": [
    "泸沽湖：湖光山色中的摩梭风情",
    "探寻秘境泸沽湖",
    "沉浸式体验泸沽湖"
  ]
}
```

**展开查看OutputConfig**

```json
{
  "Count": 4,
  "Height": 1080,
  "Width": 1920,
  "MediaURL": "http://[your-bucket].oss-<region-id>.aliyuncs.com/[your-file-path]/[your-file-name]
_{index}_w.mp4"
}
```

### **结果示例**

| **竖屏** | **横屏** |
| ------ | ------ |
|        |        |

### **进阶配置**

进阶配置详见 [批量一键成片混剪逻辑与进阶配置](https://help.aliyun.com/zh/ims/use-cases/one-click-blend-shear-logic-and-advanced-configuration "")

## **常见问题**

脚本化自动成片常见问题请参见 [脚本化自动成片FAQ](https://help.aliyun.com/zh/ims/support/script-to-video-faq "")：

- [全局口播模式的处理逻辑是什么？](https://help.aliyun.com/zh/ims/support/script-to-video-faq#4c16c06050n2c "")

- [分组口播模式的处理逻辑是什么？](https://help.aliyun.com/zh/ims/support/script-to-video-faq#530b9ad250uqp "")

- [如何解决成片画面切换过于生硬、频率过快的问题？](https://help.aliyun.com/zh/ims/support/script-to-video-faq#064892c4503w9 "")

- [如何解决画面切换速度过快或过慢以及配置镜头时长的问题？](https://help.aliyun.com/zh/ims/support/script-to-video-faq#160ef6e450jny "")

- [如何计算图片类素材在成片中的显示时长？](https://help.aliyun.com/zh/ims/support/script-to-video-faq#e1865fda0ckgf "")

- [如何确保视频素材在成片中能够被完整播完？](https://help.aliyun.com/zh/ims/support/script-to-video-faq#9e890f382emlt "")

- [实现原声视频片段与解说交替播放的具体方法是什么？](https://help.aliyun.com/zh/ims/support/script-to-video-faq#186d9920234bu "")


## 相关文档

- [相关准备工作](https://help.aliyun.com/zh/ims/use-cases/audio-and-video-editing-and-synthesis "")

- 脚本化自动成片详见 [SubmitBatchMediaProducingJob - 批量智能一键成片](https://help.aliyun.com/zh/ims/developer-reference/api-ice-2020-11-09-submitbatchmediaproducingjob "")

- 获取脚本化自动成片详见 [GetBatchMediaProducingJob - 获取批量智能一键成片任务信息](https://help.aliyun.com/zh/ims/developer-reference/api-ice-2020-11-09-getbatchmediaproducingjob "")

- 通过脚本化自动成片制作人脸集锦视频详见 [人脸集锦视频制作教程](https://help.aliyun.com/zh/ims/use-cases/create-face-montage-videos "")

- 进阶配置详见 [批量一键成片混剪逻辑与进阶配置](https://help.aliyun.com/zh/ims/use-cases/one-click-blend-shear-logic-and-advanced-configuration "")