当您具有确定的成片结构预期和对应的素材储备时，推荐使用脚本化自动成片功能。此方法适用于电商、本地生活营销等场景，能够根据预设的脚本节点和关联素材，一键批量生成高质量的视频内容。

## **注意事项**

脚本自动化成片功能支持按预设脚本执行素材合成，不包含口播与画面的智能匹配。若要实现 **口播或脚本文案与视频画面匹配**，请使用 [智能图文匹配成片](https://help.aliyun.com/zh/ims/user-guide/image-text-matching "")。

## **功能介绍**

**说明**

- 脚本化自动成片与智能图文匹配成片共用同一个提交任务的 [OpenAPI](https://help.aliyun.com/zh/ims/developer-reference/api-ice-2020-11-09-submitbatchmediaproducingjob "")。有关如何通过参数区分这两者，请参见 [参数区别说明](https://help.aliyun.com/zh/ims/use-cases/intelligent-one-click-chip-parameter-judgment-rules "")。

- 脚本化自动成片提供了【全局口播】和【分组口播】两种主要生成模式，这两种模式针对不同场景下的视频制作需求，提供了灵活高效的生成模式。

| **成片模式** | **功能描述**                                                       | **使用场景**                                             | **匹配方式**              |
| -------- | -------------------------------------------------------------- | ---------------------------------------------------- | --------------------- |
| 全局口播模式   | 将多个完整的口播文案与视频素材随机搭配，快速生成大量风格相似的视频。强调视频的整体感觉。                   | - 制作强调连贯性和一致性、讲述完整故事的视频。<br>  <br>- 希望所有视频保持统一风格的情况。 | 整体匹配，确保视频从头到尾感觉和谐一致。  |
| 分组口播模式   | 将一个完整的口播文案拆分成多个段落，每个段落分别与视频中的不同节点匹配，确保每个片段都精确对应，适合需要精细控制内容的视频。 | - 对视频中每个部分有具体要求的情况。<br>  <br>- 需要精确表达每个环节内容的视频。      | 分段匹配，让每个片段都能精准对应口播内容。 |

## **创建脚本化自动成片任务**

### **通过控制台创建任务**

1. 登录 [智能媒体服务控制台](https://ims.console.aliyun.com/?spm=a2c4g.11186623.0.0.251d72d3yfxbTD)。

2. 在顶部左上角根据实际情况选择地域。

3. 导航至 **智能生产制作** \> **智能批量一键成片**。

4. 在“脚本化自动成片标签页”中，点击“创建脚本化自动成片”以开始创作。

5. 配置脚本节点、背景音乐、贴纸、标题、口播区域及合成配置，具体操作详见下方表格。

6. 点击 **发起智能任务** 以提交任务。


| **成片模式** | **使用介绍**                                                                                                                                                                                                                                                                                                                                                                 |
| -------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| 全局口播模式   | - 在脚本节点配置区域，添加脚本节点，设置节点描述并关联节点媒资（必填）；<br>  <br>- 在背景音乐区域，添加背景音乐（非必填，未填写时默认使用官方音乐）；<br>  <br>- 在贴纸区域，添加图片素材，作为整个合成视频的贴纸或水印信息，支持添加多个，合成每个视频时随机1个生效（非必填）；<br>  <br>- 在标题区域，添加标题文本，支持通过AIGC基于关键词生成文本，支持添加多个文本内容，合成每个视频时随机1个生效（非必填）；<br>  <br>- 在口播区域，添加口播文本，支持通过AIGC基于关键词生成文本，支持添加多个文本内容，合成每个视频时随机1个生效（非必填）；<br>  <br>- 在合成配置区域，填写预期合成数量、合成文件命名规则、合成存储路径等，即可发起脚本化自动成片任务（必填）； |
| 分组口播模式   | - 在脚本节点配置区域，添加脚本节点，设置节点描述并关联节点媒资。同时可以对每一个媒资分组可分别设置多个口播文案，合成时候随机取1个生效（必填）；<br>  <br>- 在背景音乐区域，添加背景音乐（非必填，未填写时默认使用官方音乐）；<br>  <br>- 在贴纸区域，添加图片素材，作为整个合成视频的贴纸或水印信息，支持添加多个，合成每个视频时随机1个生效（非必填）；<br>  <br>- 在标题区域，添加标题文本，支持通过AIGC基于关键词生成文本，支持添加多个文本内容，合成每个视频时随机1个生效（非必填）；<br>  <br>- 在合成配置区域，填写预期合成数量、合成文件命名规则、合成存储路径等，即可发起脚本化自动成片任务（必填）；                                      |

### **通过API创建任务**

- 提交脚本化成片任务： [SubmitBatchMediaProducingJob - 批量智能一键成片](https://help.aliyun.com/zh/ims/developer-reference/api-ice-2020-11-09-submitbatchmediaproducingjob "")、 [脚本化自动成片参数说明](https://help.aliyun.com/zh/ims/use-cases/scripted-auto-slice "")

- 查询任务列表： [ListBatchMediaProducingJobs - 列出符合条件的一键成片任务](https://help.aliyun.com/zh/ims/developer-reference/api-ice-2020-11-09-listbatchmediaproducingjobs "")

- 查询任务信息： [GetBatchMediaProducingJob - 获取批量智能一键成片任务信息](https://help.aliyun.com/zh/ims/developer-reference/api-ice-2020-11-09-getbatchmediaproducingjob "")

- 文案、标题智能生成： [SubmitTextGenerateJob - 提交关键词扩写任务](https://help.aliyun.com/zh/ims/developer-reference/api-ice-2020-11-09-submittextgeneratejob "")


### **高级配置选项**

**说明**

对于希望对合成视频进行更强个性化定制的用户，可以通过高级配置选项调整字幕样式、进出场动效、转场、特效、配音效果以及匹配策略等，从而提升视频的视觉效果。

|                 |                                                                                                                                                         |
| --------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **通过API进行参数设置** | 如果通过API创建任务，具体参数配置参见 [批量一键成片混剪逻辑与进阶配置](https://help.aliyun.com/zh/ims/use-cases/one-click-blend-shear-logic-and-advanced-configuration "")              |
| **通过控制台进行参数设置** | 如果通过控制台创建任务，您可以在创建任务时，在页面右侧的 **剪辑策略高级设置** 标签页中，按照页面提示进行配置。![image](https://help-static-aliyun-doc.aliyuncs.com/assets/img/zh-CN/2081979371/p917376.png) |

## **应用示意**

### **全局口播模式**

如下图所示，视频预期介绍哈尔滨旅游，视频结构明确为“城市风貌”、“历史底蕴”、“吃喝玩乐”，每个节点关联对应的图片/视频素材，系统将按照结构顺序整体排布， **各节点随机选择素材，搭配口播文稿，** 进行时长自适应，一键批量生成成百上千个不同的视频。

![image](https://help-static-aliyun-doc.aliyuncs.com/assets/img/zh-CN/0037891371/p874050.png)

### **分组口播模式**

如下图所示，视频预期介绍哈尔滨旅游，视频结构明确为“城市风貌”、“历史底蕴”、“吃喝玩乐”，每个节点关联对应的图片/视频素材，系统将按照结构顺序整体排布， **各节点随机选择素材和各自节点中的口播文稿**，进行时长自适应，一键批量生成成百上千个不同的视频。![image](https://help-static-aliyun-doc.aliyuncs.com/assets/img/zh-CN/0037891371/p874051.png)
